===============================================
Quick Test Guide - Advanced Free Scale Tool v2.0
English Version
===============================================

This guide helps you quickly test the tool after installation.

===============================================
STEP 1: VERIFY INSTALLATION
===============================================

1. Open AutoCAD
2. Type: FREESCALE
3. You should see: "=== Advanced Free Scale Tool ==="
4. If you get "Unknown command", the tool is not loaded properly

===============================================
STEP 2: CREATE TEST OBJECTS
===============================================

Create some test objects to scale:

1. Draw a rectangle:
   Command: RECTANGLE
   First corner: 0,0
   Second corner: 100,50

2. Draw a circle:
   Command: CIRCLE
   Center: 150,25
   Radius: 20

3. Draw a line:
   Command: LINE
   From: 200,0
   To: 250,50

===============================================
STEP 3: TEST MAIN TOOL (FREESCALE)
===============================================

1. Type: FREESCALE
2. Select the rectangle (click on it)
3. Press Enter to confirm selection
4. The GUI dialog should appear showing:
   - Current Width: ~100.00 mm
   - Current Height: ~50.00 mm

5. In the "New Width" field, enter: 150
6. In the "New Height" field, enter: 75
7. Click "Preview" to see the change
8. Click "Apply" to make it permanent

Expected Result: Rectangle becomes 150mm wide and 75mm tall

===============================================
STEP 4: TEST QUICK SCALE (QUICKSCALE)
===============================================

1. Type: QUICKSCALE
2. Select the circle
3. Press Enter to confirm selection
4. You should see current measurements displayed
5. Choose option "1" (change width only)
6. Enter new width: 60
7. Press Enter

Expected Result: Circle becomes oval (60mm wide, original height)

===============================================
STEP 5: TEST MEASURE TOOL (MEASURE)
===============================================

1. Type: MEASURE
2. Select any object
3. Press Enter to confirm selection
4. You should see measurements displayed:
   - Width in mm
   - Height in mm
   - Area in mm²
5. A bounding rectangle should be drawn around the object

===============================================
STEP 6: TEST UNIT CONVERTER (CONVERT)
===============================================

1. Type: CONVERT
2. Enter value: 100
3. From unit: mm
4. To unit: cm
5. Result should be: 10.0000 cm

===============================================
TROUBLESHOOTING COMMON ISSUES:
===============================================

Issue: "Unknown command FREESCALE"
Solution: 
- Type APPLOAD
- Load FreeScale_EN.lsp
- Try again

Issue: "Cannot load dialog"
Solution:
- Make sure FreeScale_EN.dcl is in the Support folder
- Both .lsp and .dcl files must be in the same location

Issue: Measurements seem wrong
Solution:
- Check your drawing units (UNITS command)
- The tool auto-detects units and converts to mm

Issue: GUI doesn't appear
Solution:
- Verify both files are installed
- Try reloading with APPLOAD

Issue: Objects disappear after scaling
Solution:
- Use Ctrl+Z to undo
- Check that scale values are reasonable (not zero or negative)

===============================================
ADVANCED TESTING:
===============================================

Test Base Point Selection:
1. Use FREESCALE on a rectangle
2. Try different base point options:
   - Left-Bottom (default)
   - Center-Center
   - Right-Top
3. Notice how the object scales from different points

Test Aspect Ratio:
1. Use FREESCALE on a circle
2. Check "Maintain Aspect Ratio"
3. Change width - height should change proportionally
4. Uncheck it and change width - height stays the same

Test Preview Mode:
1. Use FREESCALE on any object
2. Enter new measurements
3. Click "Preview"
4. Use Ctrl+Z to undo the preview
5. Modify measurements and preview again

===============================================
PERFORMANCE BENCHMARKS:
===============================================

The tool should handle:
- Single objects: Instant response
- Multiple objects (10-50): 1-2 seconds
- Complex objects: 2-5 seconds
- Large selections (100+): 5-10 seconds

If performance is slower, check:
- Drawing complexity
- Number of objects selected
- AutoCAD system performance

===============================================
SUCCESS CRITERIA:
===============================================

✅ All commands work without errors
✅ GUI appears and responds correctly
✅ Measurements are displayed in millimeters
✅ Scaling works accurately
✅ Preview mode functions properly
✅ Base point selection works
✅ Unit conversion is accurate

If all tests pass, the tool is working correctly!

===============================================
NEXT STEPS:
===============================================

1. Read the full Installation_Guide_EN.txt for detailed usage
2. Try the tool on your actual drawings
3. Experiment with different base points and options
4. Save frequently used scale ratios as presets

===============================================

For technical support, refer to the troubleshooting section in the main documentation.
