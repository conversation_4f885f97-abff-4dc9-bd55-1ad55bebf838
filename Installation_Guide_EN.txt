===============================================
Advanced Free Scale Tool v2.0 - Installation Guide
English Version with Units Fix
===============================================

DESCRIPTION:
Advanced tool with GUI interface that allows independent scaling of objects 
in AutoCAD with millimeter measurements and visual base point selection.

NEW FEATURES:
✅ English GUI interface
✅ Automatic units detection and conversion
✅ Current and new measurements display in millimeters
✅ Visual base point selection (9 positions + custom)
✅ Interactive preview of changes
✅ Optional aspect ratio maintenance
✅ Save and load favorite ratios
✅ Measurement and unit conversion tools

===============================================
MANUAL INSTALLATION:
===============================================

Method 1 - Automatic Installation (Recommended):
1. Run install_freescale_EN.bat
2. Follow on-screen instructions
3. Files will be copied automatically to the correct folder

Method 2 - Manual Installation:
1. Copy both files FreeScale_EN.lsp and FreeScale_EN.dcl to AutoCAD Support folder
   (Usually: C:\Users\<USER>\AppData\Roaming\Autodesk\AutoCAD [version]\Support)

2. In AutoCAD, type command: APPLOAD
3. Select FreeScale_EN.lsp file and click Load
4. For automatic loading: click "Add to Startup Suite"

IMPORTANT: Make sure FreeScale_EN.dcl file is in the same folder as FreeScale_EN.lsp

===============================================
AVAILABLE COMMANDS (ALL IN ENGLISH):
===============================================

🎯 FREESCALE - Main tool (Recommended)
   - Advanced GUI interface
   - Current and new measurements display in millimeters
   - Visual base point selection (9 positions)
   - Interactive preview of changes
   - Optional aspect ratio maintenance
   - Save and load favorite ratios

📏 QUICKSCALE - Quick scaling with millimeters
   - Direct input of new measurements in millimeters
   - Quick scaling options
   - Width only, height only, or both
   - Percentage input

📐 MEASURE - Measure objects
   - Display measurements of selected objects in millimeters
   - Area calculation
   - Draw bounding rectangle for reference

🔄 CONVERT - Unit converter
   - Convert between millimeters, centimeters, meters, and inches
   - Helper tool for calculations

===============================================
USAGE EXAMPLES:
===============================================

Example 1: Resize a rectangle to make it wider
- Type: FREESCALE
- Select the rectangle
- The GUI will show current measurements
- Enter new width in the "New Width" field
- Choose base point (e.g., bottom-left)
- Click "Preview" to see the result
- Click "Apply" if satisfied

Example 2: Convert a circle to oval
- Type: FREESCALE
- Select the circle
- Enter new width: 150 mm
- Enter new height: 80 mm
- Choose center as base point
- Click "Apply"

Example 3: Quick scaling
- Type: QUICKSCALE
- Select objects
- Choose option 1 (change width only)
- Enter new width: 200 mm
- Result: Width becomes 200mm, height unchanged

===============================================
GUI INTERFACE EXPLANATION:
===============================================

Current Measurements:
- Shows current width and height in millimeters
- Calculated automatically when objects are selected
- Read-only fields

New Measurements:
- Enter desired width and height in millimeters
- Can modify width and height independently
- "Calc %" button for percentage input

Scale Factors:
- Automatically calculated from measurements
- Shows scaling ratio for each direction
- Read-only fields

Base Point:
- Choose position that won't move during scaling
- 9 predefined positions available
- Or pick custom point with mouse

Options:
- "Maintain Aspect Ratio": keeps width-to-height ratio
- "Preview Mode": shows changes before final application

Control Buttons:
- Preview: Apply changes temporarily (use Ctrl+Z to undo)
- Apply: Apply changes permanently
- Reset: Return to original values
- Cancel: Exit without changes

===============================================
UNITS HANDLING:
===============================================

The tool automatically detects your drawing units and converts to millimeters:

Supported Units:
- Millimeters (1:1 conversion)
- Centimeters (1:10 conversion)
- Meters (1:1000 conversion)
- Inches (1:25.4 conversion)
- Feet (1:304.8 conversion)

The tool reads the INSUNITS system variable to determine your drawing units
and automatically converts all measurements to millimeters for display.

===============================================
TROUBLESHOOTING:
===============================================

Problem: "Unknown command FREESCALE"
Solution: Make sure FreeScale_EN.lsp is loaded with APPLOAD

Problem: "Cannot load dialog FreeScale_EN.dcl"
Solution: Ensure FreeScale_EN.dcl file is in the AutoCAD Support folder

Problem: Measurements seem wrong
Solution: Check your drawing units setting (UNITS command)

Problem: Can't enter decimal numbers
Solution: Use period (.) not comma (,) for decimal point

Problem: Objects disappear after scaling
Solution: Check that scale values are not zero or extremely small

Problem: GUI doesn't appear
Solution: Make sure both .lsp and .dcl files are in the Support folder

===============================================
KEYBOARD SHORTCUTS:
===============================================

Tab: Move between fields
Enter: Apply changes
Esc: Cancel dialog
Ctrl+Z: Undo preview

===============================================
TIPS FOR BEST RESULTS:
===============================================

💡 General Tips:
- Always use "Preview" before final application
- Save backup copy of drawing before major modifications
- Use "Maintain Aspect Ratio" for shapes that should stay proportional

🎯 Base Point Selection:
- For buildings: use bottom-left corner
- For logos: use center
- For furniture: use ground contact point

📏 Measurement Input:
- Can use decimal numbers (e.g., 123.45)
- Don't type "mm" - unit is predefined
- Use "Calc %" button for percentage input

💾 Saved Presets:
- Save common ratios like 1.5:1 or 2:1
- Give clear names to saved presets
- Can save unlimited number of presets

===============================================
FILE STRUCTURE:
===============================================

Required files:
├── FreeScale_EN.lsp          # Main AutoLISP tool
├── FreeScale_EN.dcl          # GUI dialog file
└── Installation_Guide_EN.txt # This file

Optional files:
├── install_freescale_EN.bat  # Automatic installer
└── test_shapes.scr          # Test file for creating sample shapes

===============================================
TECHNICAL REQUIREMENTS:
===============================================

- AutoCAD 2018 or later
- Windows operating system
- Administrative rights for installation
- Both .lsp and .dcl files must be in the same folder

===============================================
VERSION HISTORY:
===============================================

v2.0 English:
- Full English interface
- Automatic units detection and conversion
- Improved GUI layout
- Better error handling
- Enhanced measurement accuracy

v1.0:
- Initial release with basic scaling functionality

===============================================

For technical support, refer to AutoCAD documentation or LISP programming resources.
