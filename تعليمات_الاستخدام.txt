===============================================
أداة التكبير الحر المتقدمة v2.0 لبرنامج AutoCAD
Advanced Free Scale Tool v2.0 for AutoCAD
===============================================

الوصف:
أداة متقدمة مع واجهة رسومية تتيح لك تكبير الأشكال في AutoCAD بحرية في العرض والطول
بشكل منفصل، مع عرض القياسات بالمليمتر وإمكانية تحديد نقطة التكبير بصرياً.

المميزات الجديدة:
✅ واجهة رسومية سهلة الاستخدام
✅ عرض القياسات الحالية والجديدة بالمليمتر
✅ تحديد نقطة الأساس بصرياً (9 مواضع + نقطة مخصصة)
✅ معاينة تفاعلية للتغييرات
✅ الحفاظ على النسبة اختيارياً
✅ حفظ واستدعاء النسب المفضلة
✅ أدوات قياس وتحويل الوحدات

===============================================
طريقة التثبيت:
===============================================

الطريقة الأولى - التثبيت التلقائي (موصى بها):
1. شغّل ملف install_freescale.bat
2. اتبع التعليمات على الشاشة
3. سيتم نسخ الملفات تلقائياً إلى المجلد الصحيح

الطريقة الثانية - التثبيت اليدوي:
1. انسخ الملفين FreeScale.lsp و FreeScale.dcl إلى مجلد AutoCAD Support
   (عادة: C:\Users\<USER>\AppData\Roaming\Autodesk\AutoCAD [الإصدار]\Support)

2. في AutoCAD، اكتب الأمر: APPLOAD
3. اختر ملف FreeScale.lsp وانقر Load
4. للتحميل التلقائي: انقر "Add to Startup Suite"

ملاحظة مهمة: تأكد من وجود ملف FreeScale.dcl في نفس مجلد FreeScale.lsp

===============================================
الأوامر المتاحة:
===============================================

🎯 FREESCALEGUI - الأداة الرئيسية (موصى بها)
   - واجهة رسومية متقدمة وسهلة الاستخدام
   - عرض القياسات الحالية والجديدة بالمليمتر
   - تحديد نقطة الأساس بصرياً (9 مواضع)
   - معاينة تفاعلية للتغييرات
   - الحفاظ على النسبة اختيارياً
   - حفظ واستدعاء النسب المفضلة

📏 QUICKSCALEMM - التكبير السريع بالمليمتر
   - إدخال القياسات الجديدة مباشرة بالمليمتر
   - خيارات سريعة للتكبير
   - تكبير العرض فقط، الارتفاع فقط، أو كلاهما
   - إدخال النسب المئوية

📐 MEASUREMM - قياس الكائنات
   - عرض قياسات الكائنات المختارة بالمليمتر
   - حساب المساحة
   - رسم مربع التحديد للتوضيح

🔄 CONVERTUNITS - محول الوحدات
   - تحويل بين المليمتر والسنتيمتر والمتر والبوصة
   - أداة مساعدة للحسابات

===============================================
أمثلة على الاستخدام:
===============================================

مثال 1: تكبير مستطيل ليصبح أعرض
- اكتب: FREESCALE2
- اختر المستطيل
- حدد نقطة الأساس (عادة الزاوية السفلى اليسرى)
- اختر X لتعديل العرض
- أدخل 2.0 لمضاعفة العرض
- اختر A لتطبيق التغيير

مثال 2: تكبير دائرة لتصبح بيضاوية
- اكتب: FREESCALE2
- اختر الدائرة
- حدد نقطة المركز
- اختر X وأدخل 1.5 للعرض
- اختر Y وأدخل 0.8 للطول
- اختر A لتطبيق التغيير

===============================================
نصائح مهمة:
===============================================

1. اختر نقطة الأساس بعناية - هي النقطة التي لن تتحرك أثناء التكبير
2. القيم أكبر من 1.0 تعني تكبير، أقل من 1.0 تعني تصغير
3. القيمة 1.0 تعني عدم تغيير في ذلك الاتجاه
4. يمكن استخدام قيم سالبة لعكس الاتجاه
5. احفظ نسخة احتياطية من الرسم قبل التطبيق

===============================================
حل المشاكل الشائعة:
===============================================

مشكلة: الأداة لا تعمل
الحل: تأكد من تحميل الملف بشكل صحيح باستخدام APPLOAD

مشكلة: النتيجة غير متوقعة
الحل: تحقق من نقطة الأساس والقيم المدخلة

مشكلة: الكائن يختفي
الحل: تحقق من أن القيم المدخلة ليست صفر أو قريبة جداً من الصفر

===============================================
معلومات إضافية:
===============================================

- الأداة تعمل مع جميع أنواع الكائنات في AutoCAD
- يمكن تطبيقها على عدة كائنات في نفس الوقت
- تحافظ على الخصائص الأخرى للكائنات (اللون، النوع، إلخ)
- متوافقة مع جميع إصدارات AutoCAD الحديثة

للدعم الفني أو الاستفسارات، يرجى الرجوع إلى وثائق AutoCAD أو مجتمع المطورين.
