===============================================
أداة التكبير الحر لبرنامج AutoCAD
Free Scale Tool for AutoCAD
===============================================

الوصف:
هذه الأداة تتيح لك تكبير الأشكال في AutoCAD بحرية في العرض والطول بشكل منفصل،
بدلاً من التكبير بنسبة ثابتة موحدة على جميع الاتجاهات.

===============================================
طريقة التثبيت:
===============================================

1. انسخ ملف FreeScale.lsp إلى مجلد AutoCAD Support Files
   (عادة: C:\Users\<USER>\AppData\Roaming\Autodesk\AutoCAD [الإصدار]\Support)

2. في AutoCAD، اكتب الأمر: APPLOAD
3. اختر ملف FreeScale.lsp وانقر Load
4. أو يمكنك كتابة: (load "FreeScale.lsp") في سطر الأوامر

===============================================
الأوامر المتاحة:
===============================================

1. FREESCALE - الأداة الأساسية
   - اختر الكائنات المراد تكبيرها
   - حدد نقطة الأساس للتكبير
   - أدخل معامل التكبير للعرض (X)
   - أدخل معامل التكبير للطول (Y)

2. FREESCALE2 - الأداة المتقدمة (موصى بها)
   - واجهة تفاعلية أسهل في الاستخدام
   - إمكانية تعديل القيم قبل التطبيق
   - خيارات متعددة للتحكم

3. QUICKSCALE - التكبير السريع
   - خيارات محددة مسبقاً للتكبير السريع
   - تكبير العرض فقط أو الطول فقط

===============================================
أمثلة على الاستخدام:
===============================================

مثال 1: تكبير مستطيل ليصبح أعرض
- اكتب: FREESCALE2
- اختر المستطيل
- حدد نقطة الأساس (عادة الزاوية السفلى اليسرى)
- اختر X لتعديل العرض
- أدخل 2.0 لمضاعفة العرض
- اختر A لتطبيق التغيير

مثال 2: تكبير دائرة لتصبح بيضاوية
- اكتب: FREESCALE2
- اختر الدائرة
- حدد نقطة المركز
- اختر X وأدخل 1.5 للعرض
- اختر Y وأدخل 0.8 للطول
- اختر A لتطبيق التغيير

===============================================
نصائح مهمة:
===============================================

1. اختر نقطة الأساس بعناية - هي النقطة التي لن تتحرك أثناء التكبير
2. القيم أكبر من 1.0 تعني تكبير، أقل من 1.0 تعني تصغير
3. القيمة 1.0 تعني عدم تغيير في ذلك الاتجاه
4. يمكن استخدام قيم سالبة لعكس الاتجاه
5. احفظ نسخة احتياطية من الرسم قبل التطبيق

===============================================
حل المشاكل الشائعة:
===============================================

مشكلة: الأداة لا تعمل
الحل: تأكد من تحميل الملف بشكل صحيح باستخدام APPLOAD

مشكلة: النتيجة غير متوقعة
الحل: تحقق من نقطة الأساس والقيم المدخلة

مشكلة: الكائن يختفي
الحل: تحقق من أن القيم المدخلة ليست صفر أو قريبة جداً من الصفر

===============================================
معلومات إضافية:
===============================================

- الأداة تعمل مع جميع أنواع الكائنات في AutoCAD
- يمكن تطبيقها على عدة كائنات في نفس الوقت
- تحافظ على الخصائص الأخرى للكائنات (اللون، النوع، إلخ)
- متوافقة مع جميع إصدارات AutoCAD الحديثة

للدعم الفني أو الاستفسارات، يرجى الرجوع إلى وثائق AutoCAD أو مجتمع المطورين.
