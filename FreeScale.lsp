;; FreeScale.lsp - أداة التكبير الحر المتقدمة للأشكال في AutoCAD
;; تتيح تكبير الأشكال بحرية مع واجهة رسومية وقياسات بالمليمتر
;; Advanced Free Scale Tool for AutoCAD with GUI and millimeter measurements

;; متغيرات عامة للأداة
(setq *fs-current-width* 0.0)
(setq *fs-current-height* 0.0)
(setq *fs-new-width* 0.0)
(setq *fs-new-height* 0.0)
(setq *fs-base-point* nil)
(setq *fs-selected-objects* nil)
(setq *fs-preview-mode* nil)

;; دالة حساب الحدود الخارجية للكائنات المختارة
(defun get-objects-bounds (ss / i ent bbox min-pt max-pt all-min all-max)
  (setq all-min nil all-max nil)
  (setq i 0)

  (repeat (sslength ss)
    (setq ent (ssname ss i))
    (setq bbox (get-entity-bounds ent))

    (if bbox
      (progn
        (setq min-pt (car bbox))
        (setq max-pt (cadr bbox))

        (if (not all-min)
          (progn
            (setq all-min min-pt)
            (setq all-max max-pt)
          )
          (progn
            ;; تحديث النقاط الدنيا والعليا
            (setq all-min (list
              (min (car all-min) (car min-pt))
              (min (cadr all-min) (cadr min-pt))
              (min (caddr all-min) (caddr min-pt))
            ))
            (setq all-max (list
              (max (car all-max) (car max-pt))
              (max (cadr all-max) (cadr max-pt))
              (max (caddr all-max) (caddr max-pt))
            ))
          )
        )
      )
    )
    (setq i (1+ i))
  )

  (if (and all-min all-max)
    (list all-min all-max)
    nil
  )
)

;; دالة حساب الحدود الخارجية لكائن واحد
(defun get-entity-bounds (ent / entdata obj)
  (setq entdata (entget ent))

  (cond
    ;; خط
    ((= (cdr (assoc 0 entdata)) "LINE")
     (list
       (cdr (assoc 10 entdata))
       (cdr (assoc 11 entdata))
     )
    )

    ;; دائرة
    ((= (cdr (assoc 0 entdata)) "CIRCLE")
     (setq center (cdr (assoc 10 entdata)))
     (setq radius (cdr (assoc 40 entdata)))
     (list
       (list (- (car center) radius) (- (cadr center) radius) (caddr center))
       (list (+ (car center) radius) (+ (cadr center) radius) (caddr center))
     )
    )

    ;; مستطيل أو بوليلاين
    ((or (= (cdr (assoc 0 entdata)) "LWPOLYLINE")
         (= (cdr (assoc 0 entdata)) "POLYLINE"))
     (get-polyline-bounds ent)
    )

    ;; نص
    ((= (cdr (assoc 0 entdata)) "TEXT")
     (setq pt (cdr (assoc 10 entdata)))
     (setq height (cdr (assoc 40 entdata)))
     (list pt (list (+ (car pt) (* height 5)) (+ (cadr pt) height) (caddr pt)))
    )

    ;; افتراضي - استخدام نقطة الإدراج
    (T
     (setq pt (cdr (assoc 10 entdata)))
     (if pt
       (list pt pt)
       nil
     )
    )
  )
)

;; دالة حساب حدود البوليلاين
(defun get-polyline-bounds (ent / entdata vertices min-pt max-pt)
  (setq entdata (entget ent))
  (setq vertices '())
  (setq min-pt nil max-pt nil)

  ;; جمع جميع النقاط
  (foreach item entdata
    (if (= (car item) 10)
      (setq vertices (append vertices (list (cdr item))))
    )
  )

  ;; حساب الحد الأدنى والأعلى
  (foreach vertex vertices
    (if (not min-pt)
      (progn
        (setq min-pt vertex)
        (setq max-pt vertex)
      )
      (progn
        (setq min-pt (list
          (min (car min-pt) (car vertex))
          (min (cadr min-pt) (cadr vertex))
          (min (caddr min-pt) (caddr vertex))
        ))
        (setq max-pt (list
          (max (car max-pt) (car vertex))
          (max (cadr max-pt) (cadr vertex))
          (max (caddr max-pt) (caddr vertex))
        ))
      )
    )
  )

  (if (and min-pt max-pt)
    (list min-pt max-pt)
    nil
  )
)

;; دالة حساب القياسات بالمليمتر
(defun calculate-measurements-mm (bounds / width height)
  (if bounds
    (progn
      (setq width (* (- (car (cadr bounds)) (car (car bounds))) 1000))
      (setq height (* (- (cadr (cadr bounds)) (cadr (car bounds))) 1000))
      (list width height)
    )
    (list 0.0 0.0)
  )
)

;; الأداة الرئيسية الجديدة مع الواجهة الرسومية
(defun c:FREESCALEGUI (/ ss bounds measurements)
  (princ "\n=== أداة التكبير الحر المتقدمة ===")
  (princ "\nاختر الكائنات للتكبير: ")

  ;; اختيار الكائنات
  (setq ss (ssget))

  (if ss
    (progn
      ;; حفظ الكائنات المختارة
      (setq *fs-selected-objects* ss)

      ;; حساب الحدود والقياسات
      (setq bounds (get-objects-bounds ss))
      (setq measurements (calculate-measurements-mm bounds))

      ;; تعيين القيم الحالية
      (setq *fs-current-width* (car measurements))
      (setq *fs-current-height* (cadr measurements))
      (setq *fs-new-width* *fs-current-width*)
      (setq *fs-new-height* *fs-current-height*)

      ;; عرض الواجهة الرسومية
      (show-freescale-dialog)
    )
    (princ "\nلم يتم اختيار أي كائنات")
  )
  (princ)
)

;; دالة عرض الواجهة الرسومية
(defun show-freescale-dialog (/ dcl_id result)
  ;; تحميل ملف DCL
  (setq dcl_id (load_dialog "FreeScale.dcl"))

  (if (not (new_dialog "freescale_dialog" dcl_id))
    (progn
      (princ "\nخطأ: لا يمكن تحميل الواجهة الرسومية")
      (unload_dialog dcl_id)
      (exit)
    )
  )

  ;; تعيين القيم الأولية
  (set_tile "current_width" (rtos *fs-current-width* 2 2))
  (set_tile "current_height" (rtos *fs-current-height* 2 2))
  (set_tile "new_width" (rtos *fs-new-width* 2 2))
  (set_tile "new_height" (rtos *fs-new-height* 2 2))
  (update-scale-factors)

  ;; تعيين أحداث الأزرار
  (action_tile "new_width" "(update-from-width)")
  (action_tile "new_height" "(update-from-height)")
  (action_tile "calc_width" "(calculate-width-dialog)")
  (action_tile "calc_height" "(calculate-height-dialog)")
  (action_tile "pick_point" "(pick-custom-point)")
  (action_tile "preview" "(preview-scaling)")
  (action_tile "apply" "(apply-scaling-dialog)")
  (action_tile "reset" "(reset-values)")
  (action_tile "maintain_ratio" "(toggle-maintain-ratio)")

  ;; عرض الحوار
  (setq result (start_dialog))
  (unload_dialog dcl_id)

  result
)

;; دالة تحديث معاملات التكبير
(defun update-scale-factors ()
  (if (and (> *fs-current-width* 0) (> *fs-current-height* 0))
    (progn
      (set_tile "scale_x" (rtos (/ *fs-new-width* *fs-current-width*) 2 3))
      (set_tile "scale_y" (rtos (/ *fs-new-height* *fs-current-height*) 2 3))
    )
  )
)

;; دالة تحديث من العرض الجديد
(defun update-from-width ()
  (setq *fs-new-width* (atof (get_tile "new_width")))
  (if (= (get_tile "maintain_ratio") "1")
    (progn
      (setq ratio (/ *fs-new-width* *fs-current-width*))
      (setq *fs-new-height* (* *fs-current-height* ratio))
      (set_tile "new_height" (rtos *fs-new-height* 2 2))
    )
  )
  (update-scale-factors)
)

;; دالة تحديث من الارتفاع الجديد
(defun update-from-height ()
  (setq *fs-new-height* (atof (get_tile "new_height")))
  (if (= (get_tile "maintain_ratio") "1")
    (progn
      (setq ratio (/ *fs-new-height* *fs-current-height*))
      (setq *fs-new-width* (* *fs-current-width* ratio))
      (set_tile "new_width" (rtos *fs-new-width* 2 2))
    )
  )
  (update-scale-factors)
)

;; دالة حساب العرض من نسبة مئوية
(defun calculate-width-dialog ()
  (setq percentage (getreal "\nأدخل النسبة المئوية للعرض (100% = الحجم الحالي): "))
  (if percentage
    (progn
      (setq *fs-new-width* (* *fs-current-width* (/ percentage 100.0)))
      (set_tile "new_width" (rtos *fs-new-width* 2 2))
      (update-from-width)
    )
  )
)

;; دالة حساب الارتفاع من نسبة مئوية
(defun calculate-height-dialog ()
  (setq percentage (getreal "\nأدخل النسبة المئوية للارتفاع (100% = الحجم الحالي): "))
  (if percentage
    (progn
      (setq *fs-new-height* (* *fs-current-height* (/ percentage 100.0)))
      (set_tile "new_height" (rtos *fs-new-height* 2 2))
      (update-from-height)
    )
  )
)

;; دالة اختيار نقطة مخصصة
(defun pick-custom-point ()
  (done_dialog 2)  ; إغلاق الحوار مؤقتاً
  (princ "\nحدد نقطة الأساس للتكبير: ")
  (setq *fs-base-point* (getpoint))
  (if *fs-base-point*
    (progn
      (set_tile "point_coords"
        (strcat "X:" (rtos (car *fs-base-point*) 2 2)
                " Y:" (rtos (cadr *fs-base-point*) 2 2)))
      (show-freescale-dialog)  ; إعادة عرض الحوار
    )
    (show-freescale-dialog)
  )
)

;; دالة تبديل الحفاظ على النسبة
(defun toggle-maintain-ratio ()
  (if (= (get_tile "maintain_ratio") "1")
    (update-from-width)  ; إعادة حساب القيم مع الحفاظ على النسبة
  )
)

;; دالة المعاينة
(defun preview-scaling ()
  (if *fs-selected-objects*
    (progn
      (setq base-pt (calculate-base-point))
      (setq scale-x (/ *fs-new-width* *fs-current-width*))
      (setq scale-y (/ *fs-new-height* *fs-current-height*))

      ;; تطبيق التكبير مع إمكانية التراجع
      (command "_.UNDO" "Begin")
      (apply-advanced-scaling *fs-selected-objects* base-pt scale-x scale-y)
      (set_tile "status_text" "تم تطبيق المعاينة - يمكنك التراجع بـ Ctrl+Z")
    )
    (set_tile "status_text" "خطأ: لا توجد كائنات مختارة")
  )
)

;; دالة التطبيق النهائي
(defun apply-scaling-dialog ()
  (if *fs-selected-objects*
    (progn
      (setq base-pt (calculate-base-point))
      (setq scale-x (/ *fs-new-width* *fs-current-width*))
      (setq scale-y (/ *fs-new-height* *fs-current-height*))

      (apply-advanced-scaling *fs-selected-objects* base-pt scale-x scale-y)
      (done_dialog 1)  ; إغلاق الحوار
      (princ (strcat "\nتم تطبيق التكبير بنجاح!"
                    "\nالعرض: " (rtos *fs-new-width* 2 2) " مم"
                    "\nالارتفاع: " (rtos *fs-new-height* 2 2) " مم"))
    )
    (set_tile "status_text" "خطأ: لا توجد كائنات مختارة")
  )
)

;; دالة إعادة تعيين القيم
(defun reset-values ()
  (setq *fs-new-width* *fs-current-width*)
  (setq *fs-new-height* *fs-current-height*)
  (set_tile "new_width" (rtos *fs-new-width* 2 2))
  (set_tile "new_height" (rtos *fs-new-height* 2 2))
  (update-scale-factors)
  (set_tile "status_text" "تم إعادة تعيين القيم")
)
;; دالة حساب نقطة الأساس حسب الاختيار
(defun calculate-base-point (/ bounds min-pt max-pt center-x center-y base-x base-y)
  (if *fs-base-point*
    *fs-base-point*  ; استخدام النقطة المخصصة
    (progn
      ;; حساب نقطة الأساس من الخيارات المحددة
      (setq bounds (get-objects-bounds *fs-selected-objects*))
      (setq min-pt (car bounds))
      (setq max-pt (cadr bounds))
      (setq center-x (/ (+ (car min-pt) (car max-pt)) 2.0))
      (setq center-y (/ (+ (cadr min-pt) (cadr max-pt)) 2.0))

      ;; تحديد الموضع الأفقي
      (cond
        ((= (get_tile "base_left") "1") (setq base-x (car min-pt)))
        ((= (get_tile "base_center_h") "1") (setq base-x center-x))
        ((= (get_tile "base_right") "1") (setq base-x (car max-pt)))
        (T (setq base-x (car min-pt)))  ; افتراضي
      )

      ;; تحديد الموضع الرأسي
      (cond
        ((= (get_tile "base_bottom") "1") (setq base-y (cadr min-pt)))
        ((= (get_tile "base_center_v") "1") (setq base-y center-y))
        ((= (get_tile "base_top") "1") (setq base-y (cadr max-pt)))
        (T (setq base-y (cadr min-pt)))  ; افتراضي
      )

      (list base-x base-y 0.0)
    )
  )
)

;; دالة التكبير المتقدمة
(defun apply-advanced-scaling (ss base-pt scale-x scale-y / i ent)
  (if (and ss base-pt (> scale-x 0) (> scale-y 0))
    (progn
      (princ "\nجاري تطبيق التكبير المتقدم...")

      ;; تطبيق التكبير في اتجاه X أولاً
      (if (/= scale-x 1.0)
        (command "_.SCALE" ss "" base-pt scale-x)
      )

      ;; تطبيق التكبير في اتجاه Y إذا كان مختلفاً عن X
      (if (and (/= scale-y 1.0) (/= scale-y scale-x))
        (progn
          ;; حساب معامل التكبير الإضافي لـ Y
          (setq y-additional (/ scale-y scale-x))

          ;; استخدام أمر STRETCH للتكبير في اتجاه Y فقط
          (setq i 0)
          (repeat (sslength ss)
            (setq ent (ssname ss i))

            ;; تطبيق تحويل Y باستخدام مصفوفة التحويل
            (command "_.SCALE" ent "" base-pt "Reference"
                     base-pt
                     (list (car base-pt) (+ (cadr base-pt) 1.0))
                     y-additional)

            (setq i (1+ i))
          )
        )
      )

      (command "_.REGEN")
      T  ; نجح التطبيق
    )
    nil  ; فشل التطبيق
  )
)

;; دالة رسم مربعات التحديد للمعاينة
(defun draw-selection-boxes (ss base-pt / bounds min-pt max-pt)
  (if ss
    (progn
      (setq bounds (get-objects-bounds ss))
      (setq min-pt (car bounds))
      (setq max-pt (cadr bounds))

      ;; رسم مربع التحديد الحالي
      (command "_.RECTANGLE" min-pt max-pt)

      ;; رسم نقطة الأساس
      (command "_.POINT" base-pt)

      ;; رسم خطوط الاتجاه
      (command "_.LINE" base-pt (list (car max-pt) (cadr base-pt)) "")
      (command "_.LINE" base-pt (list (car base-pt) (cadr max-pt)) "")
    )
  )
)

;; أداة التكبير السريع المحسنة
(defun c:QUICKSCALEMM (/ ss bounds measurements choice new-width new-height)
  (princ "\n=== التكبير السريع بالمليمتر ===")
  (princ "\nاختر الكائنات: ")
  (setq ss (ssget))

  (if ss
    (progn
      ;; حساب القياسات الحالية
      (setq bounds (get-objects-bounds ss))
      (setq measurements (calculate-measurements-mm bounds))

      (princ (strcat "\nالقياسات الحالية:"
                    "\nالعرض: " (rtos (car measurements) 2 2) " مم"
                    "\nالارتفاع: " (rtos (cadr measurements) 2 2) " مم"))

      (princ "\nاختر نوع التكبير:")
      (princ "\n1 - تغيير العرض فقط")
      (princ "\n2 - تغيير الارتفاع فقط")
      (princ "\n3 - تغيير كلاهما")
      (princ "\n4 - نسبة مئوية")
      (princ "\nأدخل اختيارك [1/2/3/4]: ")
      (setq choice (getstring))

      (cond
        ((= choice "1")
         (princ "\nأدخل العرض الجديد بالمليمتر: ")
         (setq new-width (getreal))
         (if new-width
           (apply-quick-scale-mm ss bounds new-width (cadr measurements))
         )
        )

        ((= choice "2")
         (princ "\nأدخل الارتفاع الجديد بالمليمتر: ")
         (setq new-height (getreal))
         (if new-height
           (apply-quick-scale-mm ss bounds (car measurements) new-height)
         )
        )

        ((= choice "3")
         (princ "\nأدخل العرض الجديد بالمليمتر: ")
         (setq new-width (getreal))
         (princ "\nأدخل الارتفاع الجديد بالمليمتر: ")
         (setq new-height (getreal))
         (if (and new-width new-height)
           (apply-quick-scale-mm ss bounds new-width new-height)
         )
        )

        ((= choice "4")
         (princ "\nأدخل النسبة المئوية للعرض: ")
         (setq width-percent (getreal))
         (princ "\nأدخل النسبة المئوية للارتفاع: ")
         (setq height-percent (getreal))
         (if (and width-percent height-percent)
           (progn
             (setq new-width (* (car measurements) (/ width-percent 100.0)))
             (setq new-height (* (cadr measurements) (/ height-percent 100.0)))
             (apply-quick-scale-mm ss bounds new-width new-height)
           )
         )
        )

        (T (princ "\nخيار غير صحيح!"))
      )
    )
    (princ "\nلم يتم اختيار أي كائنات")
  )
  (princ)
)

;; دالة تطبيق التكبير السريع بالمليمتر
(defun apply-quick-scale-mm (ss bounds new-width-mm new-height-mm / current-measurements scale-x scale-y base-pt)
  (setq current-measurements (calculate-measurements-mm bounds))
  (setq scale-x (/ new-width-mm (car current-measurements)))
  (setq scale-y (/ new-height-mm (cadr current-measurements)))

  ;; استخدام الزاوية السفلى اليسرى كنقطة أساس
  (setq base-pt (car bounds))

  (if (apply-advanced-scaling ss base-pt scale-x scale-y)
    (princ (strcat "\nتم التطبيق بنجاح!"
                  "\nالعرض الجديد: " (rtos new-width-mm 2 2) " مم"
                  "\nالارتفاع الجديد: " (rtos new-height-mm 2 2) " مم"))
    (princ "\nفشل في تطبيق التكبير!")
  )
)
  
  (if ss
    (progn
      ;; تحديد نقطة الأساس
      (princ "\nحدد نقطة الأساس للتكبير: ")
      (setq pt1 (getpoint))
      
      (if pt1
        (progn
          ;; استخدام واجهة بسيطة لإدخال القيم
          (setq scaleX 1.0 scaleY 1.0)
          
          ;; حلقة تفاعلية لإدخال القيم
          (while T
            (princ (strcat "\nالقيم الحالية - العرض: " (rtos scaleX 2 2) 
                          " الطول: " (rtos scaleY 2 2)))
            (princ "\nاختر [X-العرض/Y-الطول/A-تطبيق/Q-خروج]: ")
            (setq choice (getstring))
            
            (cond
              ((or (= choice "X") (= choice "x"))
               (princ "\nأدخل معامل التكبير للعرض: ")
               (setq temp (getreal))
               (if (and temp (> temp 0)) (setq scaleX temp))
              )
              
              ((or (= choice "Y") (= choice "y"))
               (princ "\nأدخل معامل التكبير للطول: ")
               (setq temp (getreal))
               (if (and temp (> temp 0)) (setq scaleY temp))
              )
              
              ((or (= choice "A") (= choice "a"))
               ;; تطبيق التكبير
               (apply-free-scale ss pt1 scaleX scaleY)
               (exit)
              )
              
              ((or (= choice "Q") (= choice "q"))
               (princ "\nتم الإلغاء")
               (exit)
              )
              
              (T (princ "\nخيار غير صحيح!"))
            )
          )
        )
        (princ "\nتم الإلغاء - لم يتم تحديد نقطة الأساس")
      )
    )
    (princ "\nلم يتم اختيار أي كائنات")
  )
  (princ)
)

;; دالة مساعدة لتطبيق التكبير الحر المحسنة
(defun apply-free-scale (ss pt1 scaleX scaleY / ent i entlist newent)
  (princ "\nجاري تطبيق التكبير الحر...")
  (setq i 0)

  ;; بدء مجموعة التراجع
  (command "_.UNDO" "Begin")

  (repeat (sslength ss)
    (setq ent (ssname ss i))
    (setq entlist (entget ent))

    ;; تطبيق التحويل باستخدام مصفوفة التحويل
    (if (and entlist (not (null entlist)))
      (progn
        ;; إنشاء مصفوفة التحويل
        (setq transform-matrix
          (list
            (list scaleX 0.0 0.0 (* (car pt1) (- 1.0 scaleX)))
            (list 0.0 scaleY 0.0 (* (cadr pt1) (- 1.0 scaleY)))
            (list 0.0 0.0 1.0 0.0)
            (list 0.0 0.0 0.0 1.0)
          )
        )

        ;; تطبيق التحويل
        (command "_.SCALE" ent "" pt1 scaleX)

        ;; إذا كان معامل Y مختلف، نطبق تحويل إضافي
        (if (/= scaleY scaleX)
          (progn
            (setq y-adjustment (/ scaleY scaleX))
            ;; استخدام أمر STRETCH للتحويل في اتجاه Y
            (command "_.STRETCH"
                     (ssadd ent (ssadd))
                     ""
                     (list (- (car pt1) 1000) (- (cadr pt1) 1000))
                     (list (+ (car pt1) 1000) (+ (cadr pt1) 1000))
                     pt1
                     (list (car pt1) (* (cadr pt1) y-adjustment))
            )
          )
        )
      )
      (princ (strcat "\nتحذير: لا يمكن معالجة الكائن رقم " (itoa i)))
    )

    (setq i (1+ i))
  )

  ;; إنهاء مجموعة التراجع
  (command "_.UNDO" "End")

  (princ (strcat "\nتم تطبيق التكبير الحر بنجاح على " (itoa (sslength ss)) " كائن!"
                "\nمعامل العرض (X): " (rtos scaleX 2 3)
                "\nمعامل الطول (Y): " (rtos scaleY 2 3)))
)

;; دالة للتحقق من صحة القيم المدخلة
(defun validate-scale-values (scaleX scaleY / result)
  (setq result T)

  (if (or (<= scaleX 0) (<= scaleY 0))
    (progn
      (princ "\nخطأ: يجب أن تكون قيم التكبير أكبر من الصفر!")
      (setq result nil)
    )
  )

  (if (or (> scaleX 1000) (> scaleY 1000))
    (progn
      (princ "\nتحذير: قيم التكبير كبيرة جداً، قد تؤدي إلى نتائج غير متوقعة!")
      (princ "\nهل تريد المتابعة؟ [Y/N]: ")
      (if (not (= (strcase (getstring)) "Y"))
        (setq result nil)
      )
    )
  )

  result
)

;; أداة سريعة للتكبير بنسب محددة مسبقاً
(defun c:QUICKSCALE (/ ss pt1 choice)
  (princ "\n=== التكبير السريع ===")
  (princ "\nاختر الكائنات: ")
  (setq ss (ssget))
  
  (if ss
    (progn
      (princ "\nحدد نقطة الأساس: ")
      (setq pt1 (getpoint))
      
      (if pt1
        (progn
          (princ "\nاختر نسبة التكبير:")
          (princ "\n1 - تكبير العرض فقط (2:1)")
          (princ "\n2 - تكبير الطول فقط (1:2)")
          (princ "\n3 - تكبير مخصص")
          (princ "\nأدخل اختيارك [1/2/3]: ")
          (setq choice (getstring))
          
          (cond
            ((= choice "1")
             (apply-free-scale ss pt1 2.0 1.0))
            ((= choice "2")
             (apply-free-scale ss pt1 1.0 2.0))
            ((= choice "3")
             (c:FREESCALE2))
            (T (princ "\nخيار غير صحيح!"))
          )
        )
      )
    )
  )
  (princ)
)

;; أداة لحفظ واستعادة نسب التكبير المفضلة
(defun c:SAVESCALE (/ scaleX scaleY name filename)
  (princ "\n=== حفظ نسبة التكبير ===")
  (princ "\nأدخل معامل العرض (X): ")
  (setq scaleX (getreal))
  (princ "\nأدخل معامل الطول (Y): ")
  (setq scaleY (getreal))
  (princ "\nأدخل اسم النسبة: ")
  (setq name (getstring T))

  (if (and scaleX scaleY name)
    (progn
      (setq filename "scale_presets.txt")
      ;; حفظ النسبة في ملف
      (setq file (open filename "a"))
      (if file
        (progn
          (write-line (strcat name "," (rtos scaleX 2 3) "," (rtos scaleY 2 3)) file)
          (close file)
          (princ (strcat "\nتم حفظ النسبة: " name))
        )
        (princ "\nخطأ في حفظ الملف!")
      )
    )
    (princ "\nبيانات غير مكتملة!")
  )
  (princ)
)

;; أداة لتطبيق نسب محفوظة
(defun c:LOADSCALE (/ ss pt1 filename file line parts)
  (princ "\n=== تطبيق نسبة محفوظة ===")
  (setq filename "scale_presets.txt")

  (if (findfile filename)
    (progn
      (princ "\nالنسب المحفوظة:")
      (setq file (open filename "r"))
      (setq counter 1)

      ;; عرض النسب المحفوظة
      (while (setq line (read-line file))
        (setq parts (str-split line ","))
        (if (= (length parts) 3)
          (princ (strcat "\n" (itoa counter) ". " (nth 0 parts)
                        " (X:" (nth 1 parts) " Y:" (nth 2 parts) ")"))
        )
        (setq counter (1+ counter))
      )
      (close file)

      ;; اختيار النسبة
      (princ "\nأدخل رقم النسبة المطلوبة: ")
      (setq choice (getint))

      (if choice
        (progn
          ;; قراءة النسبة المختارة
          (setq file (open filename "r"))
          (setq counter 1)
          (setq found nil)

          (while (and (setq line (read-line file)) (not found))
            (if (= counter choice)
              (progn
                (setq parts (str-split line ","))
                (if (= (length parts) 3)
                  (progn
                    (setq scaleX (atof (nth 1 parts)))
                    (setq scaleY (atof (nth 2 parts)))
                    (setq found T)
                  )
                )
              )
            )
            (setq counter (1+ counter))
          )
          (close file)

          (if found
            (progn
              ;; تطبيق النسبة
              (princ "\nاختر الكائنات: ")
              (setq ss (ssget))
              (if ss
                (progn
                  (princ "\nحدد نقطة الأساس: ")
                  (setq pt1 (getpoint))
                  (if pt1
                    (apply-free-scale ss pt1 scaleX scaleY)
                  )
                )
              )
            )
            (princ "\nرقم غير صحيح!")
          )
        )
      )
    )
    (princ "\nلا توجد نسب محفوظة!")
  )
  (princ)
)

;; دالة مساعدة لتقسيم النص
(defun str-split (str delimiter / pos result)
  (setq result '())
  (while (setq pos (vl-string-search delimiter str))
    (setq result (append result (list (substr str 1 pos))))
    (setq str (substr str (+ pos 2)))
  )
  (append result (list str))
)

;; أداة للتكبير التفاعلي مع المعاينة
(defun c:PREVIEWSCALE (/ ss pt1 scaleX scaleY original-coords)
  (princ "\n=== التكبير مع المعاينة ===")
  (princ "\nاختر الكائنات: ")
  (setq ss (ssget))

  (if ss
    (progn
      (princ "\nحدد نقطة الأساس: ")
      (setq pt1 (getpoint))

      (if pt1
        (progn
          ;; حفظ الإحداثيات الأصلية
          (setq original-coords (save-original-coordinates ss))

          (setq scaleX 1.0 scaleY 1.0)
          (setq continue T)

          (while continue
            (princ (strcat "\nالقيم الحالية - X:" (rtos scaleX 2 2) " Y:" (rtos scaleY 2 2)))
            (princ "\n[X/Y/P-معاينة/A-تطبيق/R-استعادة/Q-خروج]: ")
            (setq choice (strcase (getstring)))

            (cond
              ((= choice "X")
               (princ "\nمعامل العرض: ")
               (setq temp (getreal))
               (if (and temp (> temp 0))
                 (progn
                   (setq scaleX temp)
                   ;; معاينة فورية
                   (restore-original-coordinates ss original-coords)
                   (apply-free-scale ss pt1 scaleX scaleY)
                 )
               )
              )

              ((= choice "Y")
               (princ "\nمعامل الطول: ")
               (setq temp (getreal))
               (if (and temp (> temp 0))
                 (progn
                   (setq scaleY temp)
                   ;; معاينة فورية
                   (restore-original-coordinates ss original-coords)
                   (apply-free-scale ss pt1 scaleX scaleY)
                 )
               )
              )

              ((= choice "P")
               ;; معاينة
               (restore-original-coordinates ss original-coords)
               (apply-free-scale ss pt1 scaleX scaleY)
               (princ "\nمعاينة مطبقة")
              )

              ((= choice "A")
               ;; تطبيق نهائي
               (princ "\nتم التطبيق النهائي")
               (setq continue nil)
              )

              ((= choice "R")
               ;; استعادة الأصل
               (restore-original-coordinates ss original-coords)
               (princ "\nتم استعادة الشكل الأصلي")
              )

              ((= choice "Q")
               ;; خروج مع استعادة
               (restore-original-coordinates ss original-coords)
               (princ "\nتم الإلغاء")
               (setq continue nil)
              )

              (T (princ "\nخيار غير صحيح!"))
            )
          )
        )
      )
    )
  )
  (princ)
)

;; دوال مساعدة للمعاينة
(defun save-original-coordinates (ss / i ent coords-list)
  (setq coords-list '())
  (setq i 0)
  (repeat (sslength ss)
    (setq ent (ssname ss i))
    (setq coords-list (append coords-list (list (entget ent))))
    (setq i (1+ i))
  )
  coords-list
)

(defun restore-original-coordinates (ss original-coords / i ent)
  (setq i 0)
  (repeat (sslength ss)
    (setq ent (ssname ss i))
    (entmod (nth i original-coords))
    (setq i (1+ i))
  )
  (command "_.REGEN")
)

;; أداة قياس الكائنات بالمليمتر
(defun c:MEASUREMM (/ ss bounds measurements)
  (princ "\n=== قياس الكائنات بالمليمتر ===")
  (princ "\nاختر الكائنات للقياس: ")
  (setq ss (ssget))

  (if ss
    (progn
      (setq bounds (get-objects-bounds ss))
      (setq measurements (calculate-measurements-mm bounds))

      (princ "\n===============================================")
      (princ (strcat "\nالعرض: " (rtos (car measurements) 2 2) " مم"))
      (princ (strcat "\nالارتفاع: " (rtos (cadr measurements) 2 2) " مم"))
      (princ (strcat "\nالمساحة: " (rtos (* (car measurements) (cadr measurements)) 2 2) " مم²"))
      (princ "\n===============================================")

      ;; رسم مربع التحديد للتوضيح
      (if bounds
        (progn
          (command "_.RECTANGLE" (car bounds) (cadr bounds))
          (princ "\nتم رسم مربع التحديد للتوضيح")
        )
      )
    )
    (princ "\nلم يتم اختيار أي كائنات")
  )
  (princ)
)

;; دالة مساعدة لتحويل الوحدات
(defun convert-units (value from-unit to-unit)
  (cond
    ;; من مليمتر إلى وحدات أخرى
    ((and (= from-unit "mm") (= to-unit "cm"))
     (/ value 10.0))
    ((and (= from-unit "mm") (= to-unit "m"))
     (/ value 1000.0))
    ((and (= from-unit "mm") (= to-unit "inch"))
     (/ value 25.4))

    ;; من سنتيمتر إلى وحدات أخرى
    ((and (= from-unit "cm") (= to-unit "mm"))
     (* value 10.0))
    ((and (= from-unit "cm") (= to-unit "m"))
     (/ value 100.0))
    ((and (= from-unit "cm") (= to-unit "inch"))
     (/ value 2.54))

    ;; من متر إلى وحدات أخرى
    ((and (= from-unit "m") (= to-unit "mm"))
     (* value 1000.0))
    ((and (= from-unit "m") (= to-unit "cm"))
     (* value 100.0))
    ((and (= from-unit "m") (= to-unit "inch"))
     (* value 39.3701))

    ;; من بوصة إلى وحدات أخرى
    ((and (= from-unit "inch") (= to-unit "mm"))
     (* value 25.4))
    ((and (= from-unit "inch") (= to-unit "cm"))
     (* value 2.54))
    ((and (= from-unit "inch") (= to-unit "m"))
     (/ value 39.3701))

    ;; نفس الوحدة
    (T value)
  )
)

;; أداة تحويل الوحدات
(defun c:CONVERTUNITS (/ value from-unit to-unit result)
  (princ "\n=== محول الوحدات ===")
  (princ "\nأدخل القيمة: ")
  (setq value (getreal))

  (if value
    (progn
      (princ "\nالوحدة الحالية [mm/cm/m/inch]: ")
      (setq from-unit (getstring))
      (princ "\nالوحدة المطلوبة [mm/cm/m/inch]: ")
      (setq to-unit (getstring))

      (setq result (convert-units value from-unit to-unit))
      (princ (strcat "\nالنتيجة: " (rtos result 2 4) " " to-unit))
    )
    (princ "\nقيمة غير صحيحة!")
  )
  (princ)
)

;; تحديث رسائل التحميل
(princ "\n")
(princ "===============================================")
(princ "\n    أداة التكبير الحر المتقدمة v2.0")
(princ "\n    Advanced Free Scale Tool v2.0")
(princ "\n===============================================")
(princ "\nتم تحميل الأدوات التالية بنجاح:")
(princ "\n")
(princ "\n🎯 FREESCALEGUI - الأداة الرئيسية مع واجهة رسومية")
(princ "\n   (الأداة الموصى بها - تُظهر القياسات بالمليمتر)")
(princ "\n")
(princ "\n📏 QUICKSCALEMM - التكبير السريع بالمليمتر")
(princ "\n   (تكبير سريع مع إدخال القياسات مباشرة)")
(princ "\n")
(princ "\n📐 MEASUREMM - قياس الكائنات بالمليمتر")
(princ "\n   (عرض قياسات الكائنات المختارة)")
(princ "\n")
(princ "\n🔄 CONVERTUNITS - محول الوحدات")
(princ "\n   (تحويل بين مم، سم، متر، بوصة)")
(princ "\n")
(princ "\n===============================================")
(princ "\nللبدء: اكتب FREESCALEGUI واختر الكائنات")
(princ "\n===============================================")
(princ "\n")

;; تحقق من وجود ملف DCL
(if (not (findfile "FreeScale.dcl"))
  (progn
    (princ "\n⚠️  تحذير: ملف FreeScale.dcl غير موجود!")
    (princ "\nيرجى التأكد من وجود الملف في نفس مجلد FreeScale.lsp")
    (princ "\nأو في مجلد AutoCAD Support")
  )
)
