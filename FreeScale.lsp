;; FreeScale.lsp - أداة التكبير الحر للأشكال في AutoCAD
;; تتيح تكبير الأشكال بحرية في العرض والطول بشكل منفصل
;; Free Scale Tool for AutoCAD - Allows independent scaling in X and Y directions

(defun c:FREESCALE (/ ss pt1 pt2 scaleX scaleY ent i matrix)
  (princ "\n=== أداة التكبير الحر - Free Scale Tool ===")
  (princ "\nاختر الكائنات للتكبير الحر - Select objects for free scaling: ")
  
  ;; اختيار الكائنات
  (setq ss (ssget))
  
  (if ss
    (progn
      ;; تحديد نقطة الأساس للتكبير
      (princ "\nحدد نقطة الأساس للتكبير - Specify base point for scaling: ")
      (setq pt1 (getpoint))
      
      (if pt1
        (progn
          ;; إدخال معامل التكبير للعرض (X)
          (princ "\nأدخل معامل التكبير للعرض (X-Scale Factor) [افتراضي 1.0]: ")
          (setq scaleX (getreal))
          (if (not scaleX) (setq scaleX 1.0))
          
          ;; إدخال معامل التكبير للطول (Y)
          (princ "\nأدخل معامل التكبير للطول (Y-Scale Factor) [افتراضي 1.0]: ")
          (setq scaleY (getreal))
          (if (not scaleY) (setq scaleY 1.0))
          
          ;; التحقق من صحة القيم
          (if (and (> scaleX 0) (> scaleY 0))
            (progn
              ;; إنشاء مصفوفة التحويل
              (setq matrix (list 
                (list scaleX 0.0 0.0 (- (* (car pt1) scaleX) (car pt1)))
                (list 0.0 scaleY 0.0 (- (* (cadr pt1) scaleY) (cadr pt1)))
                (list 0.0 0.0 1.0 0.0)
                (list 0.0 0.0 0.0 1.0)
              ))
              
              ;; تطبيق التحويل على جميع الكائنات المختارة
              (setq i 0)
              (repeat (sslength ss)
                (setq ent (ssname ss i))
                (command "_.SCALE" ent "" pt1 scaleX)
                (if (/= scaleY scaleX)
                  (progn
                    ;; تطبيق تكبير إضافي في اتجاه Y فقط
                    (command "_.STRETCH" 
                             (ssadd ent) 
                             "" 
                             pt1 
                             (list (car pt1) (+ (cadr pt1) (* (/ scaleY scaleX) 100)))
                    )
                  )
                )
                (setq i (1+ i))
              )
              
              (princ (strcat "\nتم تطبيق التكبير الحر: العرض=" 
                           (rtos scaleX 2 2) 
                           " الطول=" 
                           (rtos scaleY 2 2)))
            )
            (princ "\nخطأ: يجب أن تكون قيم التكبير أكبر من الصفر!")
          )
        )
        (princ "\nتم الإلغاء - لم يتم تحديد نقطة الأساس")
      )
    )
    (princ "\nلم يتم اختيار أي كائنات")
  )
  (princ)
)

;; أداة التكبير الحر المتقدمة مع واجهة تفاعلية
(defun c:FREESCALE2 (/ ss pt1 scaleX scaleY ent i dcl_id)
  (princ "\n=== أداة التكبير الحر المتقدمة ===")
  
  ;; اختيار الكائنات أولاً
  (princ "\nاختر الكائنات للتكبير الحر: ")
  (setq ss (ssget))
  
  (if ss
    (progn
      ;; تحديد نقطة الأساس
      (princ "\nحدد نقطة الأساس للتكبير: ")
      (setq pt1 (getpoint))
      
      (if pt1
        (progn
          ;; استخدام واجهة بسيطة لإدخال القيم
          (setq scaleX 1.0 scaleY 1.0)
          
          ;; حلقة تفاعلية لإدخال القيم
          (while T
            (princ (strcat "\nالقيم الحالية - العرض: " (rtos scaleX 2 2) 
                          " الطول: " (rtos scaleY 2 2)))
            (princ "\nاختر [X-العرض/Y-الطول/A-تطبيق/Q-خروج]: ")
            (setq choice (getstring))
            
            (cond
              ((or (= choice "X") (= choice "x"))
               (princ "\nأدخل معامل التكبير للعرض: ")
               (setq temp (getreal))
               (if (and temp (> temp 0)) (setq scaleX temp))
              )
              
              ((or (= choice "Y") (= choice "y"))
               (princ "\nأدخل معامل التكبير للطول: ")
               (setq temp (getreal))
               (if (and temp (> temp 0)) (setq scaleY temp))
              )
              
              ((or (= choice "A") (= choice "a"))
               ;; تطبيق التكبير
               (apply-free-scale ss pt1 scaleX scaleY)
               (exit)
              )
              
              ((or (= choice "Q") (= choice "q"))
               (princ "\nتم الإلغاء")
               (exit)
              )
              
              (T (princ "\nخيار غير صحيح!"))
            )
          )
        )
        (princ "\nتم الإلغاء - لم يتم تحديد نقطة الأساس")
      )
    )
    (princ "\nلم يتم اختيار أي كائنات")
  )
  (princ)
)

;; دالة مساعدة لتطبيق التكبير الحر المحسنة
(defun apply-free-scale (ss pt1 scaleX scaleY / ent i entlist newent)
  (princ "\nجاري تطبيق التكبير الحر...")
  (setq i 0)

  ;; بدء مجموعة التراجع
  (command "_.UNDO" "Begin")

  (repeat (sslength ss)
    (setq ent (ssname ss i))
    (setq entlist (entget ent))

    ;; تطبيق التحويل باستخدام مصفوفة التحويل
    (if (and entlist (not (null entlist)))
      (progn
        ;; إنشاء مصفوفة التحويل
        (setq transform-matrix
          (list
            (list scaleX 0.0 0.0 (* (car pt1) (- 1.0 scaleX)))
            (list 0.0 scaleY 0.0 (* (cadr pt1) (- 1.0 scaleY)))
            (list 0.0 0.0 1.0 0.0)
            (list 0.0 0.0 0.0 1.0)
          )
        )

        ;; تطبيق التحويل
        (command "_.SCALE" ent "" pt1 scaleX)

        ;; إذا كان معامل Y مختلف، نطبق تحويل إضافي
        (if (/= scaleY scaleX)
          (progn
            (setq y-adjustment (/ scaleY scaleX))
            ;; استخدام أمر STRETCH للتحويل في اتجاه Y
            (command "_.STRETCH"
                     (ssadd ent (ssadd))
                     ""
                     (list (- (car pt1) 1000) (- (cadr pt1) 1000))
                     (list (+ (car pt1) 1000) (+ (cadr pt1) 1000))
                     pt1
                     (list (car pt1) (* (cadr pt1) y-adjustment))
            )
          )
        )
      )
      (princ (strcat "\nتحذير: لا يمكن معالجة الكائن رقم " (itoa i)))
    )

    (setq i (1+ i))
  )

  ;; إنهاء مجموعة التراجع
  (command "_.UNDO" "End")

  (princ (strcat "\nتم تطبيق التكبير الحر بنجاح على " (itoa (sslength ss)) " كائن!"
                "\nمعامل العرض (X): " (rtos scaleX 2 3)
                "\nمعامل الطول (Y): " (rtos scaleY 2 3)))
)

;; دالة للتحقق من صحة القيم المدخلة
(defun validate-scale-values (scaleX scaleY / result)
  (setq result T)

  (if (or (<= scaleX 0) (<= scaleY 0))
    (progn
      (princ "\nخطأ: يجب أن تكون قيم التكبير أكبر من الصفر!")
      (setq result nil)
    )
  )

  (if (or (> scaleX 1000) (> scaleY 1000))
    (progn
      (princ "\nتحذير: قيم التكبير كبيرة جداً، قد تؤدي إلى نتائج غير متوقعة!")
      (princ "\nهل تريد المتابعة؟ [Y/N]: ")
      (if (not (= (strcase (getstring)) "Y"))
        (setq result nil)
      )
    )
  )

  result
)

;; أداة سريعة للتكبير بنسب محددة مسبقاً
(defun c:QUICKSCALE (/ ss pt1 choice)
  (princ "\n=== التكبير السريع ===")
  (princ "\nاختر الكائنات: ")
  (setq ss (ssget))
  
  (if ss
    (progn
      (princ "\nحدد نقطة الأساس: ")
      (setq pt1 (getpoint))
      
      (if pt1
        (progn
          (princ "\nاختر نسبة التكبير:")
          (princ "\n1 - تكبير العرض فقط (2:1)")
          (princ "\n2 - تكبير الطول فقط (1:2)")
          (princ "\n3 - تكبير مخصص")
          (princ "\nأدخل اختيارك [1/2/3]: ")
          (setq choice (getstring))
          
          (cond
            ((= choice "1")
             (apply-free-scale ss pt1 2.0 1.0))
            ((= choice "2")
             (apply-free-scale ss pt1 1.0 2.0))
            ((= choice "3")
             (c:FREESCALE2))
            (T (princ "\nخيار غير صحيح!"))
          )
        )
      )
    )
  )
  (princ)
)

;; أداة لحفظ واستعادة نسب التكبير المفضلة
(defun c:SAVESCALE (/ scaleX scaleY name filename)
  (princ "\n=== حفظ نسبة التكبير ===")
  (princ "\nأدخل معامل العرض (X): ")
  (setq scaleX (getreal))
  (princ "\nأدخل معامل الطول (Y): ")
  (setq scaleY (getreal))
  (princ "\nأدخل اسم النسبة: ")
  (setq name (getstring T))

  (if (and scaleX scaleY name)
    (progn
      (setq filename "scale_presets.txt")
      ;; حفظ النسبة في ملف
      (setq file (open filename "a"))
      (if file
        (progn
          (write-line (strcat name "," (rtos scaleX 2 3) "," (rtos scaleY 2 3)) file)
          (close file)
          (princ (strcat "\nتم حفظ النسبة: " name))
        )
        (princ "\nخطأ في حفظ الملف!")
      )
    )
    (princ "\nبيانات غير مكتملة!")
  )
  (princ)
)

;; أداة لتطبيق نسب محفوظة
(defun c:LOADSCALE (/ ss pt1 filename file line parts)
  (princ "\n=== تطبيق نسبة محفوظة ===")
  (setq filename "scale_presets.txt")

  (if (findfile filename)
    (progn
      (princ "\nالنسب المحفوظة:")
      (setq file (open filename "r"))
      (setq counter 1)

      ;; عرض النسب المحفوظة
      (while (setq line (read-line file))
        (setq parts (str-split line ","))
        (if (= (length parts) 3)
          (princ (strcat "\n" (itoa counter) ". " (nth 0 parts)
                        " (X:" (nth 1 parts) " Y:" (nth 2 parts) ")"))
        )
        (setq counter (1+ counter))
      )
      (close file)

      ;; اختيار النسبة
      (princ "\nأدخل رقم النسبة المطلوبة: ")
      (setq choice (getint))

      (if choice
        (progn
          ;; قراءة النسبة المختارة
          (setq file (open filename "r"))
          (setq counter 1)
          (setq found nil)

          (while (and (setq line (read-line file)) (not found))
            (if (= counter choice)
              (progn
                (setq parts (str-split line ","))
                (if (= (length parts) 3)
                  (progn
                    (setq scaleX (atof (nth 1 parts)))
                    (setq scaleY (atof (nth 2 parts)))
                    (setq found T)
                  )
                )
              )
            )
            (setq counter (1+ counter))
          )
          (close file)

          (if found
            (progn
              ;; تطبيق النسبة
              (princ "\nاختر الكائنات: ")
              (setq ss (ssget))
              (if ss
                (progn
                  (princ "\nحدد نقطة الأساس: ")
                  (setq pt1 (getpoint))
                  (if pt1
                    (apply-free-scale ss pt1 scaleX scaleY)
                  )
                )
              )
            )
            (princ "\nرقم غير صحيح!")
          )
        )
      )
    )
    (princ "\nلا توجد نسب محفوظة!")
  )
  (princ)
)

;; دالة مساعدة لتقسيم النص
(defun str-split (str delimiter / pos result)
  (setq result '())
  (while (setq pos (vl-string-search delimiter str))
    (setq result (append result (list (substr str 1 pos))))
    (setq str (substr str (+ pos 2)))
  )
  (append result (list str))
)

;; أداة للتكبير التفاعلي مع المعاينة
(defun c:PREVIEWSCALE (/ ss pt1 scaleX scaleY original-coords)
  (princ "\n=== التكبير مع المعاينة ===")
  (princ "\nاختر الكائنات: ")
  (setq ss (ssget))

  (if ss
    (progn
      (princ "\nحدد نقطة الأساس: ")
      (setq pt1 (getpoint))

      (if pt1
        (progn
          ;; حفظ الإحداثيات الأصلية
          (setq original-coords (save-original-coordinates ss))

          (setq scaleX 1.0 scaleY 1.0)
          (setq continue T)

          (while continue
            (princ (strcat "\nالقيم الحالية - X:" (rtos scaleX 2 2) " Y:" (rtos scaleY 2 2)))
            (princ "\n[X/Y/P-معاينة/A-تطبيق/R-استعادة/Q-خروج]: ")
            (setq choice (strcase (getstring)))

            (cond
              ((= choice "X")
               (princ "\nمعامل العرض: ")
               (setq temp (getreal))
               (if (and temp (> temp 0))
                 (progn
                   (setq scaleX temp)
                   ;; معاينة فورية
                   (restore-original-coordinates ss original-coords)
                   (apply-free-scale ss pt1 scaleX scaleY)
                 )
               )
              )

              ((= choice "Y")
               (princ "\nمعامل الطول: ")
               (setq temp (getreal))
               (if (and temp (> temp 0))
                 (progn
                   (setq scaleY temp)
                   ;; معاينة فورية
                   (restore-original-coordinates ss original-coords)
                   (apply-free-scale ss pt1 scaleX scaleY)
                 )
               )
              )

              ((= choice "P")
               ;; معاينة
               (restore-original-coordinates ss original-coords)
               (apply-free-scale ss pt1 scaleX scaleY)
               (princ "\nمعاينة مطبقة")
              )

              ((= choice "A")
               ;; تطبيق نهائي
               (princ "\nتم التطبيق النهائي")
               (setq continue nil)
              )

              ((= choice "R")
               ;; استعادة الأصل
               (restore-original-coordinates ss original-coords)
               (princ "\nتم استعادة الشكل الأصلي")
              )

              ((= choice "Q")
               ;; خروج مع استعادة
               (restore-original-coordinates ss original-coords)
               (princ "\nتم الإلغاء")
               (setq continue nil)
              )

              (T (princ "\nخيار غير صحيح!"))
            )
          )
        )
      )
    )
  )
  (princ)
)

;; دوال مساعدة للمعاينة
(defun save-original-coordinates (ss / i ent coords-list)
  (setq coords-list '())
  (setq i 0)
  (repeat (sslength ss)
    (setq ent (ssname ss i))
    (setq coords-list (append coords-list (list (entget ent))))
    (setq i (1+ i))
  )
  coords-list
)

(defun restore-original-coordinates (ss original-coords / i ent)
  (setq i 0)
  (repeat (sslength ss)
    (setq ent (ssname ss i))
    (entmod (nth i original-coords))
    (setq i (1+ i))
  )
  (command "_.REGEN")
)

(princ "\n===============================================")
(princ "\nتم تحميل أدوات التكبير الحر المتقدمة:")
(princ "\n===============================================")
(princ "\nFREESCALE - أداة التكبير الحر الأساسية")
(princ "\nFREESCALE2 - أداة التكبير الحر المتقدمة")
(princ "\nQUICKSCALE - أداة التكبير السريع")
(princ "\nSAVESCALE - حفظ نسب التكبير المفضلة")
(princ "\nLOADSCALE - تطبيق نسب محفوظة")
(princ "\nPREVIEWSCALE - التكبير مع المعاينة التفاعلية")
(princ "\n===============================================")
(princ "\nاكتب اسم الأمر لاستخدام الأداة")
(princ "\n===============================================")
