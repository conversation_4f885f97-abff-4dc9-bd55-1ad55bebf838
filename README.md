# Advanced Free Scale Tool for AutoCAD v2.0

A powerful AutoLISP tool with GUI interface that allows independent scaling of objects in X and Y directions, with automatic units detection and millimeter measurements.

## 🌟 New Features v2.0

- **English GUI Interface**: Complete English version with proper units handling
- **Automatic Units Detection**: Automatically detects and converts drawing units to millimeters
- **Visual Base Point Selection**: Choose from 9 predefined positions or pick custom point
- **Real-time Measurements**: Display current and new measurements in millimeters
- **Interactive Preview**: See changes before applying them permanently
- **Units Conversion**: Built-in converter for mm, cm, m, and inches
- **Improved Accuracy**: Better calculation algorithms for precise scaling

## 📋 Available Versions

### English Version (Recommended)
- **Files**: `FreeScale_EN.lsp` + `FreeScale_EN.dcl`
- **Commands**: `FREESCALE`, `QUICKSCALE`, `MEASURE`, `CONVERT`
- **Features**: Full English interface, automatic units handling

### Arabic Version
- **Files**: `FreeScale.lsp` + `FreeScale.dcl`
- **Commands**: `FREESCALEGUI`, `QUICKSCALEMM`, `MEASUREMM`, `CONVERTUNITS`
- **Features**: Arabic interface with English fallback

## 🚀 Quick Start (English Version)

### Automatic Installation
1. Run `install_freescale_EN.bat`
2. Follow the on-screen instructions
3. Files will be copied to AutoCAD Support folder automatically

### Manual Installation
1. Copy both `FreeScale_EN.lsp` and `FreeScale_EN.dcl` to your AutoCAD Support folder
   - Usually: `%APPDATA%\Autodesk\AutoCAD [VERSION]\Support`
2. In AutoCAD, type `APPLOAD`
3. Select `FreeScale_EN.lsp` and click Load
4. Optionally, add to Startup Suite for automatic loading

⚠️ **Important**: Both .lsp and .dcl files must be in the same folder!

## 🎮 Available Commands (English)

| Command | Description |
|---------|-------------|
| `FREESCALE` | **Main tool with GUI** - Shows measurements in mm, visual base point selection |
| `QUICKSCALE` | **Quick scaling** - Direct millimeter input, fast scaling options |
| `MEASURE` | **Measure objects** - Display dimensions in mm, calculate area |
| `CONVERT` | **Unit converter** - Convert between mm, cm, m, inches |

## 🎮 Available Commands (Arabic)

| Command | Description |
|---------|-------------|
| `FREESCALEGUI` | الأداة الرئيسية مع واجهة رسومية |
| `QUICKSCALEMM` | التكبير السريع بالمليمتر |
| `MEASUREMM` | قياس الكائنات بالمليمتر |
| `CONVERTUNITS` | محول الوحدات |

## Usage Examples

### Basic Usage
```
Command: FREESCALE2
Select objects: [select objects]
Specify base point: [pick point]
Choose [X-width/Y-height/A-apply/Q-quit]: X
Enter X-scale factor: 2.0
Choose [X-width/Y-height/A-apply/Q-quit]: Y  
Enter Y-scale factor: 0.5
Choose [X-width/Y-height/A-apply/Q-quit]: A
```

### Quick Scaling
```
Command: QUICKSCALE
Select objects: [select objects]
Specify base point: [pick point]
Choose scale ratio:
1 - Width only (2:1)
2 - Height only (1:2)  
3 - Custom
Enter choice [1/2/3]: 1
```

## Common Use Cases

1. **Architectural Design**: Adjust window widths without changing heights
2. **Logo Design**: Convert circular logos to oval shapes
3. **Furniture Design**: Create different size variants
4. **Site Planning**: Expand areas in specific directions
5. **Technical Drawing**: Correct imported drawing proportions

## Scale Factor Guidelines

- Values > 1.0 = Enlargement
- Values < 1.0 = Reduction  
- Value = 1.0 = No change
- Negative values = Mirror effect

## Tips for Best Results

1. **Choose Base Point Carefully**: This point remains fixed during scaling
2. **Use Preview Mode**: Test changes before final application
3. **Save Backup**: Always save your drawing before major modifications
4. **Group Related Objects**: Group objects that should scale together
5. **Common Ratios**: Use golden ratio (1.618:1) for aesthetic proportions

## File Structure

```
├── FreeScale.lsp              # Main AutoLISP tool
├── install_freescale.bat     # Automatic installer
├── README.md                 # This file
├── تعليمات_الاستخدام.txt      # Arabic instructions
└── أمثلة_عملية.txt            # Arabic practical examples
```

## Troubleshooting

**Problem**: Tool doesn't load
**Solution**: Ensure file is in correct Support folder and use APPLOAD

**Problem**: Unexpected results
**Solution**: Check base point selection and scale values

**Problem**: Objects disappear
**Solution**: Verify scale values are not zero or extremely small

**Problem**: Command not recognized
**Solution**: Reload the LISP file using APPLOAD

## Technical Requirements

- AutoCAD 2018 or later
- Windows operating system
- Administrative rights for installation

## Advanced Features

### Scale Presets
Save frequently used scale ratios for quick access:
```
Command: SAVESCALE
Enter X-scale factor: 1.5
Enter Y-scale factor: 0.8
Enter preset name: Wide Rectangle
```

### Interactive Preview
Real-time preview of scaling changes:
```
Command: PREVIEWSCALE
[X/Y/P-preview/A-apply/R-restore/Q-quit]: P
Preview applied
```

## License

This tool is provided as-is for educational and professional use. Feel free to modify and distribute according to your needs.

## Support

For technical support or feature requests:
1. Check the troubleshooting section
2. Review the Arabic documentation files
3. Consult AutoCAD documentation for LISP programming

## Version History

- v1.0: Initial release with basic scaling functionality
- v1.1: Added interactive preview and preset management
- v1.2: Enhanced error handling and bilingual support

---

**Note**: This tool modifies AutoCAD objects. Always maintain backups of important drawings before use.
