@echo off
chcp 65001 >nul
echo ===============================================
echo أداة التكبير الحر لبرنامج AutoCAD
echo Free Scale Tool for AutoCAD - Installation
echo ===============================================
echo.

echo جاري البحث عن مجلد AutoCAD Support...
echo Searching for AutoCAD Support folder...
echo.

REM البحث عن مجلدات AutoCAD المختلفة
set "SUPPORT_FOUND=0"
set "AUTOCAD_VERSIONS=2024 2023 2022 2021 2020 2019 2018"

for %%v in (%AUTOCAD_VERSIONS%) do (
    set "SUPPORT_PATH=%APPDATA%\Autodesk\AutoCAD %%v\R24.2\enu\Support"
    if exist "!SUPPORT_PATH!" (
        echo تم العثور على مجلد AutoCAD %%v Support
        echo Found AutoCAD %%v Support folder: !SUPPORT_PATH!
        set "SUPPORT_FOUND=1"
        goto :COPY_FILES
    )
)

REM البحث في مجلدات أخرى محتملة
for %%v in (%AUTOCAD_VERSIONS%) do (
    set "SUPPORT_PATH=%APPDATA%\Autodesk\AutoCAD %%v\Support"
    if exist "!SUPPORT_PATH!" (
        echo تم العثور على مجلد AutoCAD %%v Support
        echo Found AutoCAD %%v Support folder: !SUPPORT_PATH!
        set "SUPPORT_FOUND=1"
        goto :COPY_FILES
    )
)

if "%SUPPORT_FOUND%"=="0" (
    echo.
    echo تحذير: لم يتم العثور على مجلد AutoCAD Support تلقائياً
    echo Warning: AutoCAD Support folder not found automatically
    echo.
    echo يرجى نسخ الملفات التالية يدوياً إلى مجلد AutoCAD Support:
    echo Please copy the following files manually to your AutoCAD Support folder:
    echo - FreeScale.lsp
    echo.
    echo مجلد AutoCAD Support عادة يكون في:
    echo AutoCAD Support folder is usually located at:
    echo %APPDATA%\Autodesk\AutoCAD [VERSION]\Support
    echo.
    pause
    goto :END
)

:COPY_FILES
echo.
echo جاري نسخ الملفات...
echo Copying files...

if exist "FreeScale.lsp" (
    copy "FreeScale.lsp" "%SUPPORT_PATH%\" >nul
    if %ERRORLEVEL%==0 (
        echo ✓ تم نسخ FreeScale.lsp بنجاح
        echo ✓ FreeScale.lsp copied successfully
    ) else (
        echo ✗ فشل في نسخ FreeScale.lsp
        echo ✗ Failed to copy FreeScale.lsp
    )
) else (
    echo ✗ لم يتم العثور على ملف FreeScale.lsp
    echo ✗ FreeScale.lsp file not found
)

if exist "FreeScale.dcl" (
    copy "FreeScale.dcl" "%SUPPORT_PATH%\" >nul
    if %ERRORLEVEL%==0 (
        echo ✓ تم نسخ FreeScale.dcl بنجاح
        echo ✓ FreeScale.dcl copied successfully
    ) else (
        echo ✗ فشل في نسخ FreeScale.dcl
        echo ✗ Failed to copy FreeScale.dcl
    )
) else (
    echo ✗ لم يتم العثور على ملف FreeScale.dcl
    echo ✗ FreeScale.dcl file not found
    echo ⚠️  تحذير: الواجهة الرسومية لن تعمل بدون هذا الملف
    echo ⚠️  Warning: GUI will not work without this file
)

echo.
echo ===============================================
echo تعليمات التفعيل في AutoCAD:
echo Activation Instructions in AutoCAD:
echo ===============================================
echo.
echo 1. افتح برنامج AutoCAD
echo    Open AutoCAD
echo.
echo 2. اكتب الأمر: APPLOAD
echo    Type command: APPLOAD
echo.
echo 3. اختر ملف FreeScale.lsp من القائمة
echo    Select FreeScale.lsp from the list
echo.
echo 4. انقر Load ثم Close
echo    Click Load then Close
echo.
echo 5. اكتب أحد الأوامر التالية لاستخدام الأداة:
echo    Type one of the following commands to use the tool:
echo    - FREESCALEGUI (الأداة الرئيسية - Main Tool)
echo    - QUICKSCALEMM (التكبير السريع - Quick Scale)
echo    - MEASUREMM (القياس - Measure)
echo    - CONVERTUNITS (تحويل الوحدات - Convert Units)
echo.
echo ===============================================
echo للتحميل التلقائي عند بدء AutoCAD:
echo For automatic loading when AutoCAD starts:
echo ===============================================
echo.
echo 1. اكتب الأمر: APPLOAD
echo    Type command: APPLOAD
echo.
echo 2. اختر FreeScale.lsp
echo    Select FreeScale.lsp
echo.
echo 3. انقر Add to Startup Suite
echo    Click Add to Startup Suite
echo.
echo 4. انقر Close
echo    Click Close
echo.
echo الآن ستتم تحميل الأداة تلقائياً عند بدء AutoCAD
echo Now the tool will load automatically when AutoCAD starts
echo.

:END
echo ===============================================
echo انتهى التثبيت!
echo Installation Complete!
echo ===============================================
echo.
echo للحصول على المساعدة، راجع الملفات التالية:
echo For help, refer to the following files:
echo - تعليمات_الاستخدام.txt (دليل عام)
echo - دليل_الواجهة_الرسومية.txt (شرح الواجهة)
echo - أمثلة_عملية.txt (أمثلة متقدمة)
echo - README.md (English documentation)
echo.
pause
