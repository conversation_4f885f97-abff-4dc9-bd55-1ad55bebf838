// FreeScale.dcl - واجهة رسومية لأداة التكبير الحر
// GUI Dialog for Free Scale Tool

freescale_dialog : dialog {
    label = "أداة التكبير الحر - Free Scale Tool v2.0";
    initial_focus = "current_width";
    
    // مجموعة المعلومات الحالية
    : boxed_column {
        label = "القياسات الحالية - Current Measurements";
        
        : row {
            : text {
                label = "العرض الحالي (مم):";
                width = 15;
            }
            : edit_box {
                key = "current_width";
                edit_width = 10;
                is_enabled = false;
            }
            : text {
                label = "mm";
                width = 3;
            }
        }
        
        : row {
            : text {
                label = "الارتفاع الحالي (مم):";
                width = 15;
            }
            : edit_box {
                key = "current_height";
                edit_width = 10;
                is_enabled = false;
            }
            : text {
                label = "mm";
                width = 3;
            }
        }
    }
    
    // مجموعة القياسات الجديدة
    : boxed_column {
        label = "القياسات الجديدة - New Measurements";
        
        : row {
            : text {
                label = "العرض الجديد (مم):";
                width = 15;
            }
            : edit_box {
                key = "new_width";
                edit_width = 10;
                value = "";
            }
            : text {
                label = "mm";
                width = 3;
            }
            : button {
                key = "calc_width";
                label = "حساب";
                width = 6;
            }
        }
        
        : row {
            : text {
                label = "الارتفاع الجديد (مم):";
                width = 15;
            }
            : edit_box {
                key = "new_height";
                edit_width = 10;
                value = "";
            }
            : text {
                label = "mm";
                width = 3;
            }
            : button {
                key = "calc_height";
                label = "حساب";
                width = 6;
            }
        }
    }
    
    // مجموعة معاملات التكبير
    : boxed_column {
        label = "معاملات التكبير - Scale Factors";
        
        : row {
            : text {
                label = "معامل العرض:";
                width = 12;
            }
            : edit_box {
                key = "scale_x";
                edit_width = 8;
                is_enabled = false;
            }
            : text {
                label = "معامل الارتفاع:";
                width = 12;
            }
            : edit_box {
                key = "scale_y";
                edit_width = 8;
                is_enabled = false;
            }
        }
    }
    
    // مجموعة نقطة الأساس
    : boxed_column {
        label = "نقطة الأساس - Base Point";
        
        : row {
            : radio_column {
                label = "الموضع الأفقي:";
                
                : radio_button {
                    key = "base_left";
                    label = "يسار - Left";
                    value = "1";
                }
                : radio_button {
                    key = "base_center_h";
                    label = "وسط - Center";
                }
                : radio_button {
                    key = "base_right";
                    label = "يمين - Right";
                }
            }
            
            : radio_column {
                label = "الموضع الرأسي:";
                
                : radio_button {
                    key = "base_bottom";
                    label = "أسفل - Bottom";
                    value = "1";
                }
                : radio_button {
                    key = "base_center_v";
                    label = "وسط - Center";
                }
                : radio_button {
                    key = "base_top";
                    label = "أعلى - Top";
                }
            }
        }
        
        : row {
            : button {
                key = "pick_point";
                label = "اختيار نقطة مخصصة - Pick Custom Point";
                width = 30;
            }
            : text {
                key = "point_coords";
                label = "لم يتم تحديد نقطة";
                width = 20;
            }
        }
    }
    
    // مجموعة الخيارات
    : boxed_row {
        label = "خيارات - Options";
        
        : toggle {
            key = "maintain_ratio";
            label = "الحفاظ على النسبة - Maintain Aspect Ratio";
        }
        
        : toggle {
            key = "preview_mode";
            label = "وضع المعاينة - Preview Mode";
            value = "1";
        }
    }
    
    // النسب المحفوظة
    : boxed_column {
        label = "النسب المحفوظة - Saved Presets";
        
        : row {
            : popup_list {
                key = "preset_list";
                width = 25;
                edit_width = 25;
            }
            : button {
                key = "load_preset";
                label = "تحميل";
                width = 8;
            }
            : button {
                key = "save_preset";
                label = "حفظ";
                width = 8;
            }
        }
    }
    
    // أزرار التحكم
    : row {
        : button {
            key = "preview";
            label = "معاينة - Preview";
            width = 12;
        }
        : button {
            key = "apply";
            label = "تطبيق - Apply";
            width = 12;
            is_default = true;
        }
        : button {
            key = "reset";
            label = "إعادة تعيين - Reset";
            width = 12;
        }
        : cancel_button {
            label = "إلغاء - Cancel";
            width = 12;
        }
    }
    
    // شريط الحالة
    : text {
        key = "status_text";
        label = "جاهز - Ready";
        alignment = centered;
    }
}
