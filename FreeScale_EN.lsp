;; FreeScale_EN.lsp - Advanced Free Scale Tool for AutoCAD (English Version)
;; Allows independent scaling of objects in X and Y directions with GUI and millimeter measurements
;; Version 2.0 - English Interface

;; Global variables for the tool
(setq *fs-current-width* 0.0)
(setq *fs-current-height* 0.0)
(setq *fs-new-width* 0.0)
(setq *fs-new-height* 0.0)
(setq *fs-base-point* nil)
(setq *fs-selected-objects* nil)
(setq *fs-preview-mode* nil)
(setq *fs-drawing-units* 1.0)  ; Drawing units factor

;; Function to get drawing units factor
(defun get-drawing-units-factor (/ units-value)
  (setq units-value (getvar "INSUNITS"))
  (cond
    ((= units-value 1) 25.4)    ; Inches to mm
    ((= units-value 2) 304.8)   ; Feet to mm  
    ((= units-value 4) 1.0)     ; Millimeters
    ((= units-value 5) 10.0)    ; Centimeters to mm
    ((= units-value 6) 1000.0)  ; Meters to mm
    ((= units-value 14) 1.0)    ; Micrometers (treat as mm)
    (T 1.0)                     ; Default to mm
  )
)

;; Function to calculate object bounds
(defun get-objects-bounds (ss / i ent bbox min-pt max-pt all-min all-max)
  (setq all-min nil all-max nil)
  (setq i 0)
  
  (repeat (sslength ss)
    (setq ent (ssname ss i))
    (setq bbox (get-entity-bounds ent))
    
    (if bbox
      (progn
        (setq min-pt (car bbox))
        (setq max-pt (cadr bbox))
        
        (if (not all-min)
          (progn
            (setq all-min min-pt)
            (setq all-max max-pt)
          )
          (progn
            (setq all-min (list 
              (min (car all-min) (car min-pt))
              (min (cadr all-min) (cadr min-pt))
              (min (caddr all-min) (caddr min-pt))
            ))
            (setq all-max (list 
              (max (car all-max) (car max-pt))
              (max (cadr all-max) (cadr max-pt))
              (max (caddr all-max) (caddr max-pt))
            ))
          )
        )
      )
    )
    (setq i (1+ i))
  )
  
  (if (and all-min all-max)
    (list all-min all-max)
    nil
  )
)

;; Function to get entity bounds
(defun get-entity-bounds (ent / entdata obj)
  (setq entdata (entget ent))
  
  (cond
    ;; Line
    ((= (cdr (assoc 0 entdata)) "LINE")
     (list 
       (cdr (assoc 10 entdata))
       (cdr (assoc 11 entdata))
     )
    )
    
    ;; Circle
    ((= (cdr (assoc 0 entdata)) "CIRCLE")
     (setq center (cdr (assoc 10 entdata)))
     (setq radius (cdr (assoc 40 entdata)))
     (list 
       (list (- (car center) radius) (- (cadr center) radius) (caddr center))
       (list (+ (car center) radius) (+ (cadr center) radius) (caddr center))
     )
    )
    
    ;; Rectangle or Polyline
    ((or (= (cdr (assoc 0 entdata)) "LWPOLYLINE")
         (= (cdr (assoc 0 entdata)) "POLYLINE"))
     (get-polyline-bounds ent)
    )
    
    ;; Text
    ((= (cdr (assoc 0 entdata)) "TEXT")
     (setq pt (cdr (assoc 10 entdata)))
     (setq height (cdr (assoc 40 entdata)))
     (list pt (list (+ (car pt) (* height 5)) (+ (cadr pt) height) (caddr pt)))
    )
    
    ;; Default - use insertion point
    (T
     (setq pt (cdr (assoc 10 entdata)))
     (if pt
       (list pt pt)
       nil
     )
    )
  )
)

;; Function to get polyline bounds
(defun get-polyline-bounds (ent / entdata vertices min-pt max-pt)
  (setq entdata (entget ent))
  (setq vertices '())
  (setq min-pt nil max-pt nil)
  
  ;; Collect all vertices
  (foreach item entdata
    (if (= (car item) 10)
      (setq vertices (append vertices (list (cdr item))))
    )
  )
  
  ;; Calculate min and max
  (foreach vertex vertices
    (if (not min-pt)
      (progn
        (setq min-pt vertex)
        (setq max-pt vertex)
      )
      (progn
        (setq min-pt (list 
          (min (car min-pt) (car vertex))
          (min (cadr min-pt) (cadr vertex))
          (min (caddr min-pt) (caddr vertex))
        ))
        (setq max-pt (list 
          (max (car max-pt) (car vertex))
          (max (cadr max-pt) (cadr vertex))
          (max (caddr max-pt) (caddr vertex))
        ))
      )
    )
  )
  
  (if (and min-pt max-pt)
    (list min-pt max-pt)
    nil
  )
)

;; Function to calculate measurements in millimeters
(defun calculate-measurements-mm (bounds / width height units-factor)
  (setq units-factor (get-drawing-units-factor))
  (if bounds
    (progn
      (setq width (* (- (car (cadr bounds)) (car (car bounds))) units-factor))
      (setq height (* (- (cadr (cadr bounds)) (cadr (car bounds))) units-factor))
      (list width height)
    )
    (list 0.0 0.0)
  )
)

;; Main GUI tool
(defun c:FREESCALE (/ ss bounds measurements)
  (princ "\n=== Advanced Free Scale Tool ===")
  (princ "\nSelect objects to scale: ")
  
  ;; Select objects
  (setq ss (ssget))
  
  (if ss
    (progn
      ;; Store selected objects
      (setq *fs-selected-objects* ss)
      
      ;; Calculate bounds and measurements
      (setq bounds (get-objects-bounds ss))
      (setq measurements (calculate-measurements-mm bounds))
      
      ;; Set current values
      (setq *fs-current-width* (car measurements))
      (setq *fs-current-height* (cadr measurements))
      (setq *fs-new-width* *fs-current-width*)
      (setq *fs-new-height* *fs-current-height*)
      
      ;; Show GUI dialog
      (show-freescale-dialog)
    )
    (princ "\nNo objects selected")
  )
  (princ)
)

;; Function to show GUI dialog
(defun show-freescale-dialog (/ dcl_id result)
  ;; Load DCL file
  (setq dcl_id (load_dialog "FreeScale_EN.dcl"))
  
  (if (not (new_dialog "freescale_dialog" dcl_id))
    (progn
      (princ "\nError: Cannot load GUI dialog")
      (unload_dialog dcl_id)
      (exit)
    )
  )
  
  ;; Set initial values
  (set_tile "current_width" (rtos *fs-current-width* 2 2))
  (set_tile "current_height" (rtos *fs-current-height* 2 2))
  (set_tile "new_width" (rtos *fs-new-width* 2 2))
  (set_tile "new_height" (rtos *fs-new-height* 2 2))
  (update-scale-factors)
  
  ;; Set button actions
  (action_tile "new_width" "(update-from-width)")
  (action_tile "new_height" "(update-from-height)")
  (action_tile "calc_width" "(calculate-width-dialog)")
  (action_tile "calc_height" "(calculate-height-dialog)")
  (action_tile "pick_point" "(pick-custom-point)")
  (action_tile "preview" "(preview-scaling)")
  (action_tile "apply" "(apply-scaling-dialog)")
  (action_tile "reset" "(reset-values)")
  (action_tile "maintain_ratio" "(toggle-maintain-ratio)")
  
  ;; Show dialog
  (setq result (start_dialog))
  (unload_dialog dcl_id)
  
  result
)

;; Function to update scale factors
(defun update-scale-factors ()
  (if (and (> *fs-current-width* 0) (> *fs-current-height* 0))
    (progn
      (set_tile "scale_x" (rtos (/ *fs-new-width* *fs-current-width*) 2 3))
      (set_tile "scale_y" (rtos (/ *fs-new-height* *fs-current-height*) 2 3))
    )
  )
)

;; Function to update from new width
(defun update-from-width ()
  (setq *fs-new-width* (atof (get_tile "new_width")))
  (if (= (get_tile "maintain_ratio") "1")
    (progn
      (setq ratio (/ *fs-new-width* *fs-current-width*))
      (setq *fs-new-height* (* *fs-current-height* ratio))
      (set_tile "new_height" (rtos *fs-new-height* 2 2))
    )
  )
  (update-scale-factors)
)

;; Function to update from new height
(defun update-from-height ()
  (setq *fs-new-height* (atof (get_tile "new_height")))
  (if (= (get_tile "maintain_ratio") "1")
    (progn
      (setq ratio (/ *fs-new-height* *fs-current-height*))
      (setq *fs-new-width* (* *fs-current-width* ratio))
      (set_tile "new_width" (rtos *fs-new-width* 2 2))
    )
  )
  (update-scale-factors)
)

;; Function to calculate width from percentage
(defun calculate-width-dialog ()
  (setq percentage (getreal "\nEnter width percentage (100% = current size): "))
  (if percentage
    (progn
      (setq *fs-new-width* (* *fs-current-width* (/ percentage 100.0)))
      (set_tile "new_width" (rtos *fs-new-width* 2 2))
      (update-from-width)
    )
  )
)

;; Function to calculate height from percentage
(defun calculate-height-dialog ()
  (setq percentage (getreal "\nEnter height percentage (100% = current size): "))
  (if percentage
    (progn
      (setq *fs-new-height* (* *fs-current-height* (/ percentage 100.0)))
      (set_tile "new_height" (rtos *fs-new-height* 2 2))
      (update-from-height)
    )
  )
)

;; Function to pick custom point
(defun pick-custom-point ()
  (done_dialog 2)  ; Close dialog temporarily
  (princ "\nSpecify base point for scaling: ")
  (setq *fs-base-point* (getpoint))
  (if *fs-base-point*
    (progn
      (set_tile "point_coords"
        (strcat "X:" (rtos (car *fs-base-point*) 2 2)
                " Y:" (rtos (cadr *fs-base-point*) 2 2)))
      (show-freescale-dialog)  ; Reshow dialog
    )
    (show-freescale-dialog)
  )
)

;; Function to toggle maintain ratio
(defun toggle-maintain-ratio ()
  (if (= (get_tile "maintain_ratio") "1")
    (update-from-width)  ; Recalculate values maintaining ratio
  )
)

;; Function to calculate base point from selection
(defun calculate-base-point (/ bounds min-pt max-pt center-x center-y base-x base-y)
  (if *fs-base-point*
    *fs-base-point*  ; Use custom point
    (progn
      ;; Calculate base point from selected options
      (setq bounds (get-objects-bounds *fs-selected-objects*))
      (setq min-pt (car bounds))
      (setq max-pt (cadr bounds))
      (setq center-x (/ (+ (car min-pt) (car max-pt)) 2.0))
      (setq center-y (/ (+ (cadr min-pt) (cadr max-pt)) 2.0))

      ;; Determine horizontal position
      (cond
        ((= (get_tile "base_left") "1") (setq base-x (car min-pt)))
        ((= (get_tile "base_center_h") "1") (setq base-x center-x))
        ((= (get_tile "base_right") "1") (setq base-x (car max-pt)))
        (T (setq base-x (car min-pt)))  ; Default
      )

      ;; Determine vertical position
      (cond
        ((= (get_tile "base_bottom") "1") (setq base-y (cadr min-pt)))
        ((= (get_tile "base_center_v") "1") (setq base-y center-y))
        ((= (get_tile "base_top") "1") (setq base-y (cadr max-pt)))
        (T (setq base-y (cadr min-pt)))  ; Default
      )

      (list base-x base-y 0.0)
    )
  )
)

;; Function to apply advanced scaling
(defun apply-advanced-scaling (ss base-pt scale-x scale-y / i ent units-factor)
  (setq units-factor (get-drawing-units-factor))

  (if (and ss base-pt (> scale-x 0) (> scale-y 0))
    (progn
      (princ "\nApplying advanced scaling...")

      ;; Convert scale factors based on drawing units
      (setq actual-scale-x scale-x)
      (setq actual-scale-y scale-y)

      ;; Apply X scaling first
      (if (/= actual-scale-x 1.0)
        (command "_.SCALE" ss "" base-pt actual-scale-x)
      )

      ;; Apply Y scaling if different from X
      (if (and (/= actual-scale-y 1.0) (/= actual-scale-y actual-scale-x))
        (progn
          ;; Calculate additional Y scaling factor
          (setq y-additional (/ actual-scale-y actual-scale-x))

          ;; Use STRETCH command for Y-only scaling
          (setq i 0)
          (repeat (sslength ss)
            (setq ent (ssname ss i))

            ;; Apply Y transformation using matrix
            (command "_.SCALE" ent "" base-pt "Reference"
                     base-pt
                     (list (car base-pt) (+ (cadr base-pt) 1.0))
                     y-additional)

            (setq i (1+ i))
          )
        )
      )

      (command "_.REGEN")
      T  ; Success
    )
    nil  ; Failure
  )
)

;; Function for preview scaling
(defun preview-scaling ()
  (if *fs-selected-objects*
    (progn
      (setq base-pt (calculate-base-point))
      (setq scale-x (/ *fs-new-width* *fs-current-width*))
      (setq scale-y (/ *fs-new-height* *fs-current-height*))

      ;; Apply scaling with undo capability
      (command "_.UNDO" "Begin")
      (apply-advanced-scaling *fs-selected-objects* base-pt scale-x scale-y)
      (set_tile "status_text" "Preview applied - Use Ctrl+Z to undo")
    )
    (set_tile "status_text" "Error: No objects selected")
  )
)

;; Function for final application
(defun apply-scaling-dialog ()
  (if *fs-selected-objects*
    (progn
      (setq base-pt (calculate-base-point))
      (setq scale-x (/ *fs-new-width* *fs-current-width*))
      (setq scale-y (/ *fs-new-height* *fs-current-height*))

      (apply-advanced-scaling *fs-selected-objects* base-pt scale-x scale-y)
      (done_dialog 1)  ; Close dialog
      (princ (strcat "\nScaling applied successfully!"
                    "\nNew Width: " (rtos *fs-new-width* 2 2) " mm"
                    "\nNew Height: " (rtos *fs-new-height* 2 2) " mm"))
    )
    (set_tile "status_text" "Error: No objects selected")
  )
)

;; Function to reset values
(defun reset-values ()
  (setq *fs-new-width* *fs-current-width*)
  (setq *fs-new-height* *fs-current-height*)
  (set_tile "new_width" (rtos *fs-new-width* 2 2))
  (set_tile "new_height" (rtos *fs-new-height* 2 2))
  (update-scale-factors)
  (set_tile "status_text" "Values reset to original")
)

;; Quick scale tool with millimeter input
(defun c:QUICKSCALE (/ ss bounds measurements choice new-width new-height)
  (princ "\n=== Quick Scale with Millimeters ===")
  (princ "\nSelect objects: ")
  (setq ss (ssget))

  (if ss
    (progn
      ;; Calculate current measurements
      (setq bounds (get-objects-bounds ss))
      (setq measurements (calculate-measurements-mm bounds))

      (princ (strcat "\nCurrent measurements:"
                    "\nWidth: " (rtos (car measurements) 2 2) " mm"
                    "\nHeight: " (rtos (cadr measurements) 2 2) " mm"))

      (princ "\nChoose scaling type:")
      (princ "\n1 - Change width only")
      (princ "\n2 - Change height only")
      (princ "\n3 - Change both")
      (princ "\n4 - Percentage scaling")
      (princ "\nEnter choice [1/2/3/4]: ")
      (setq choice (getstring))

      (cond
        ((= choice "1")
         (princ "\nEnter new width in millimeters: ")
         (setq new-width (getreal))
         (if new-width
           (apply-quick-scale-mm ss bounds new-width (cadr measurements))
         )
        )

        ((= choice "2")
         (princ "\nEnter new height in millimeters: ")
         (setq new-height (getreal))
         (if new-height
           (apply-quick-scale-mm ss bounds (car measurements) new-height)
         )
        )

        ((= choice "3")
         (princ "\nEnter new width in millimeters: ")
         (setq new-width (getreal))
         (princ "\nEnter new height in millimeters: ")
         (setq new-height (getreal))
         (if (and new-width new-height)
           (apply-quick-scale-mm ss bounds new-width new-height)
         )
        )

        ((= choice "4")
         (princ "\nEnter width percentage: ")
         (setq width-percent (getreal))
         (princ "\nEnter height percentage: ")
         (setq height-percent (getreal))
         (if (and width-percent height-percent)
           (progn
             (setq new-width (* (car measurements) (/ width-percent 100.0)))
             (setq new-height (* (cadr measurements) (/ height-percent 100.0)))
             (apply-quick-scale-mm ss bounds new-width new-height)
           )
         )
        )

        (T (princ "\nInvalid choice!"))
      )
    )
    (princ "\nNo objects selected")
  )
  (princ)
)

;; Function to apply quick scaling with millimeters
(defun apply-quick-scale-mm (ss bounds new-width-mm new-height-mm / current-measurements scale-x scale-y base-pt)
  (setq current-measurements (calculate-measurements-mm bounds))
  (setq scale-x (/ new-width-mm (car current-measurements)))
  (setq scale-y (/ new-height-mm (cadr current-measurements)))

  ;; Use bottom-left corner as base point
  (setq base-pt (car bounds))

  (if (apply-advanced-scaling ss base-pt scale-x scale-y)
    (princ (strcat "\nScaling applied successfully!"
                  "\nNew width: " (rtos new-width-mm 2 2) " mm"
                  "\nNew height: " (rtos new-height-mm 2 2) " mm"))
    (princ "\nFailed to apply scaling!")
  )
)

;; Measure objects in millimeters
(defun c:MEASURE (/ ss bounds measurements)
  (princ "\n=== Measure Objects in Millimeters ===")
  (princ "\nSelect objects to measure: ")
  (setq ss (ssget))

  (if ss
    (progn
      (setq bounds (get-objects-bounds ss))
      (setq measurements (calculate-measurements-mm bounds))

      (princ "\n===============================================")
      (princ (strcat "\nWidth: " (rtos (car measurements) 2 2) " mm"))
      (princ (strcat "\nHeight: " (rtos (cadr measurements) 2 2) " mm"))
      (princ (strcat "\nArea: " (rtos (* (car measurements) (cadr measurements)) 2 2) " mm²"))
      (princ "\n===============================================")

      ;; Draw bounding rectangle for visualization
      (if bounds
        (progn
          (command "_.RECTANGLE" (car bounds) (cadr bounds))
          (princ "\nBounding rectangle drawn for reference")
        )
      )
    )
    (princ "\nNo objects selected")
  )
  (princ)
)

;; Unit conversion helper function
(defun convert-units (value from-unit to-unit)
  (cond
    ;; From millimeters to other units
    ((and (= from-unit "mm") (= to-unit "cm"))
     (/ value 10.0))
    ((and (= from-unit "mm") (= to-unit "m"))
     (/ value 1000.0))
    ((and (= from-unit "mm") (= to-unit "inch"))
     (/ value 25.4))

    ;; From centimeters to other units
    ((and (= from-unit "cm") (= to-unit "mm"))
     (* value 10.0))
    ((and (= from-unit "cm") (= to-unit "m"))
     (/ value 100.0))
    ((and (= from-unit "cm") (= to-unit "inch"))
     (/ value 2.54))

    ;; From meters to other units
    ((and (= from-unit "m") (= to-unit "mm"))
     (* value 1000.0))
    ((and (= from-unit "m") (= to-unit "cm"))
     (* value 100.0))
    ((and (= from-unit "m") (= to-unit "inch"))
     (* value 39.3701))

    ;; From inches to other units
    ((and (= from-unit "inch") (= to-unit "mm"))
     (* value 25.4))
    ((and (= from-unit "inch") (= to-unit "cm"))
     (* value 2.54))
    ((and (= from-unit "inch") (= to-unit "m"))
     (/ value 39.3701))

    ;; Same unit
    (T value)
  )
)

;; Unit converter tool
(defun c:CONVERT (/ value from-unit to-unit result)
  (princ "\n=== Unit Converter ===")
  (princ "\nEnter value: ")
  (setq value (getreal))

  (if value
    (progn
      (princ "\nFrom unit [mm/cm/m/inch]: ")
      (setq from-unit (getstring))
      (princ "\nTo unit [mm/cm/m/inch]: ")
      (setq to-unit (getstring))

      (setq result (convert-units value from-unit to-unit))
      (princ (strcat "\nResult: " (rtos result 2 4) " " to-unit))
    )
    (princ "\nInvalid value!")
  )
  (princ)
)

;; Loading messages
(princ "\n")
(princ "===============================================")
(princ "\n    Advanced Free Scale Tool v2.0 (English)")
(princ "\n===============================================")
(princ "\nTools loaded successfully:")
(princ "\n")
(princ "\n🎯 FREESCALE - Main tool with GUI interface")
(princ "\n   (Recommended - Shows measurements in millimeters)")
(princ "\n")
(princ "\n📏 QUICKSCALE - Quick scaling with millimeter input")
(princ "\n   (Fast scaling with direct measurement input)")
(princ "\n")
(princ "\n📐 MEASURE - Measure objects in millimeters")
(princ "\n   (Display measurements of selected objects)")
(princ "\n")
(princ "\n🔄 CONVERT - Unit converter")
(princ "\n   (Convert between mm, cm, m, inches)")
(princ "\n")
(princ "\n===============================================")
(princ "\nTo start: Type FREESCALE and select objects")
(princ "\n===============================================")
(princ "\n")

;; Check for DCL file
(if (not (findfile "FreeScale_EN.dcl"))
  (progn
    (princ "\n⚠️  Warning: FreeScale_EN.dcl file not found!")
    (princ "\nPlease ensure the file is in the same folder as FreeScale_EN.lsp")
    (princ "\nor in the AutoCAD Support folder")
  )
)
