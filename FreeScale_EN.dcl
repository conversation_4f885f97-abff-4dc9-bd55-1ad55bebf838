// FreeScale_EN.dcl - English GUI Dialog for Free Scale Tool
// GUI Dialog for Free Scale Tool - English Version

freescale_dialog : dialog {
    label = "Advanced Free Scale Tool v2.0";
    initial_focus = "current_width";
    
    // Current measurements group
    : boxed_column {
        label = "Current Measurements";
        
        : row {
            : text {
                label = "Current Width (mm):";
                width = 18;
            }
            : edit_box {
                key = "current_width";
                edit_width = 12;
                is_enabled = false;
            }
            : text {
                label = "mm";
                width = 3;
            }
        }
        
        : row {
            : text {
                label = "Current Height (mm):";
                width = 18;
            }
            : edit_box {
                key = "current_height";
                edit_width = 12;
                is_enabled = false;
            }
            : text {
                label = "mm";
                width = 3;
            }
        }
    }
    
    // New measurements group
    : boxed_column {
        label = "New Measurements";
        
        : row {
            : text {
                label = "New Width (mm):";
                width = 18;
            }
            : edit_box {
                key = "new_width";
                edit_width = 12;
                value = "";
            }
            : text {
                label = "mm";
                width = 3;
            }
            : button {
                key = "calc_width";
                label = "Calc %";
                width = 8;
            }
        }
        
        : row {
            : text {
                label = "New Height (mm):";
                width = 18;
            }
            : edit_box {
                key = "new_height";
                edit_width = 12;
                value = "";
            }
            : text {
                label = "mm";
                width = 3;
            }
            : button {
                key = "calc_height";
                label = "Calc %";
                width = 8;
            }
        }
    }
    
    // Scale factors group
    : boxed_column {
        label = "Scale Factors";
        
        : row {
            : text {
                label = "X-Scale Factor:";
                width = 15;
            }
            : edit_box {
                key = "scale_x";
                edit_width = 10;
                is_enabled = false;
            }
            : text {
                label = "Y-Scale Factor:";
                width = 15;
            }
            : edit_box {
                key = "scale_y";
                edit_width = 10;
                is_enabled = false;
            }
        }
    }
    
    // Base point group
    : boxed_column {
        label = "Base Point";
        
        : row {
            : radio_column {
                label = "Horizontal Position:";
                
                : radio_button {
                    key = "base_left";
                    label = "Left";
                    value = "1";
                }
                : radio_button {
                    key = "base_center_h";
                    label = "Center";
                }
                : radio_button {
                    key = "base_right";
                    label = "Right";
                }
            }
            
            : radio_column {
                label = "Vertical Position:";
                
                : radio_button {
                    key = "base_bottom";
                    label = "Bottom";
                    value = "1";
                }
                : radio_button {
                    key = "base_center_v";
                    label = "Center";
                }
                : radio_button {
                    key = "base_top";
                    label = "Top";
                }
            }
        }
        
        : row {
            : button {
                key = "pick_point";
                label = "Pick Custom Point";
                width = 25;
            }
            : text {
                key = "point_coords";
                label = "No point selected";
                width = 20;
            }
        }
    }
    
    // Options group
    : boxed_row {
        label = "Options";
        
        : toggle {
            key = "maintain_ratio";
            label = "Maintain Aspect Ratio";
        }
        
        : toggle {
            key = "preview_mode";
            label = "Preview Mode";
            value = "1";
        }
    }
    
    // Saved presets group
    : boxed_column {
        label = "Saved Presets";
        
        : row {
            : popup_list {
                key = "preset_list";
                width = 25;
                edit_width = 25;
            }
            : button {
                key = "load_preset";
                label = "Load";
                width = 8;
            }
            : button {
                key = "save_preset";
                label = "Save";
                width = 8;
            }
        }
    }
    
    // Control buttons
    : row {
        : button {
            key = "preview";
            label = "Preview";
            width = 12;
        }
        : button {
            key = "apply";
            label = "Apply";
            width = 12;
            is_default = true;
        }
        : button {
            key = "reset";
            label = "Reset";
            width = 12;
        }
        : cancel_button {
            label = "Cancel";
            width = 12;
        }
    }
    
    // Status bar
    : text {
        key = "status_text";
        label = "Ready";
        alignment = centered;
    }
}
