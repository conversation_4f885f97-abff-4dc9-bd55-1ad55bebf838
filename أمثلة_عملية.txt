===============================================
أمثلة عملية لاستخدام أداة التكبير الحر
Practical Examples for Free Scale Tool
===============================================

هذا الملف يحتوي على أمثلة عملية وحالات استخدام متقدمة لأداة التكبير الحر.

===============================================
الحالة الأولى: تصميم واجهة مبنى
===============================================

المشكلة: لديك تصميم واجهة مبنى وتريد تعديل عرض النوافذ دون تغيير ارتفاعها.

الحل:
1. اكتب: FREESCALE2
2. اختر جميع النوافذ
3. حدد نقطة الأساس (الزاوية السفلى لإحدى النوافذ)
4. اختر X وأدخل 1.3 (لزيادة العرض 30%)
5. اختر Y وأدخل 1.0 (للحفاظ على الارتفاع)
6. اختر A لتطبيق التغيير

النتيجة: النوافذ ستصبح أعرض بنسبة 30% مع الحفاظ على ارتفاعها الأصلي.

===============================================
الحالة الثانية: تصميم شعار الشركة
===============================================

المشكلة: لديك شعار دائري وتريد تحويله إلى شكل بيضاوي للاستخدام في الترويسة.

الحل:
1. اكتب: PREVIEWSCALE (للمعاينة التفاعلية)
2. اختر الشعار
3. حدد نقطة المركز
4. اختر X وأدخل 2.0 (لمضاعفة العرض)
5. اختر Y وأدخل 0.6 (لتقليل الارتفاع)
6. اختر P للمعاينة
7. إذا كانت النتيجة مرضية، اختر A للتطبيق

النتيجة: شعار بيضاوي مناسب للترويسة.

===============================================
الحالة الثالثة: تصميم أثاث
===============================================

المشكلة: لديك تصميم طاولة وتريد إنشاء إصدارات مختلفة الأحجام.

الحل باستخدام النسب المحفوظة:
1. اكتب: SAVESCALE
2. أدخل 1.5 للعرض و 1.0 للطول
3. أدخل اسم "طاولة عريضة"
4. كرر العملية لحفظ نسب أخرى:
   - "طاولة طويلة": X=1.0, Y=1.8
   - "طاولة صغيرة": X=0.7, Y=0.7

للاستخدام لاحقاً:
1. اكتب: LOADSCALE
2. اختر النسبة المطلوبة من القائمة
3. اختر الطاولة وحدد نقطة الأساس

===============================================
الحالة الرابعة: تصميم مخطط موقع
===============================================

المشكلة: لديك مخطط موقع وتريد توسيع المنطقة الخضراء أفقياً فقط.

الحل:
1. اكتب: QUICKSCALE
2. اختر المنطقة الخضراء
3. حدد نقطة الأساس (الزاوية اليسرى)
4. اختر 1 (تكبير العرض فقط 2:1)

النتيجة: المنطقة الخضراء ستصبح أعرض بمقدار الضعف.

===============================================
الحالة الخامسة: تصميم مقطع معماري
===============================================

المشكلة: لديك مقطع معماري وتريد زيادة ارتفاع السقف دون تغيير العرض.

الحل المتقدم:
1. اكتب: FREESCALE2
2. اختر عناصر السقف والجدران العلوية
3. حدد نقطة الأساس (مستوى الأرض)
4. اختر X وأدخل 1.0 (بدون تغيير في العرض)
5. اختر Y وأدخل 1.2 (زيادة الارتفاع 20%)
6. اختر A لتطبيق التغيير

===============================================
نصائح متقدمة للاستخدام الاحترافي:
===============================================

1. استخدام النقاط المرجعية:
   - للمباني: استخدم زوايا الأساسات كنقاط مرجعية
   - للأثاث: استخدم نقاط الاتصال بالأرض
   - للشعارات: استخدم نقطة المركز

2. التعامل مع الكائنات المعقدة:
   - قم بتجميع الكائنات المترابطة أولاً
   - استخدم PREVIEWSCALE للتأكد من النتيجة
   - احفظ نسخة احتياطية قبل التطبيق

3. النسب الذهبية الشائعة:
   - 1.618:1 (النسبة الذهبية)
   - 16:9 (نسبة الشاشات العريضة)
   - 4:3 (النسبة التقليدية)
   - 3:2 (نسبة التصوير)

4. تجنب الأخطاء الشائعة:
   - لا تستخدم قيم صفر أو سالبة
   - تحقق من نقطة الأساس قبل التطبيق
   - استخدم UNDO إذا لم تعجبك النتيجة

===============================================
حالات استخدام خاصة:
===============================================

تصحيح الرسوم المستوردة:
- أحياناً تأتي الرسوم المستوردة بنسب خاطئة
- استخدم الأداة لتصحيح النسب حسب المقاييس الصحيحة

تكييف التصاميم للمعايير المختلفة:
- تحويل من النظام المتري إلى الإمبراطوري
- تكييف التصاميم للمعايير المحلية

إنشاء عائلات من التصاميم:
- إنشاء أحجام مختلفة من نفس التصميم
- تطوير مجموعة متنوعة من المنتجات

===============================================
استكشاف الأخطاء وإصلاحها:
===============================================

الخطأ: "لا يمكن معالجة الكائن"
الحل: تأكد من أن الكائن غير مقفل وقابل للتعديل

الخطأ: النتيجة مشوهة
الحل: تحقق من نقطة الأساس والقيم المدخلة

الخطأ: الأداة لا تستجيب
الحل: أعد تحميل الملف باستخدام APPLOAD

للحصول على أفضل النتائج، تدرب على الأمثلة البسيطة أولاً ثم انتقل للحالات المعقدة.
