@echo off
chcp 65001 >nul
echo ===============================================
echo Advanced Free Scale Tool for AutoCAD v2.0
echo English Version - Installation
echo ===============================================
echo.

echo Searching for AutoCAD Support folder...
echo.

REM Search for different AutoCAD versions
set "SUPPORT_FOUND=0"
set "AUTOCAD_VERSIONS=2024 2023 2022 2021 2020 2019 2018"

for %%v in (%AUTOCAD_VERSIONS%) do (
    set "SUPPORT_PATH=%APPDATA%\Autodesk\AutoCAD %%v\R24.2\enu\Support"
    if exist "!SUPPORT_PATH!" (
        echo Found AutoCAD %%v Support folder: !SUPPORT_PATH!
        set "SUPPORT_FOUND=1"
        goto :COPY_FILES
    )
)

REM Search in other possible folders
for %%v in (%AUTOCAD_VERSIONS%) do (
    set "SUPPORT_PATH=%APPDATA%\Autodesk\AutoCAD %%v\Support"
    if exist "!SUPPORT_PATH!" (
        echo Found AutoCAD %%v Support folder: !SUPPORT_PATH!
        set "SUPPORT_FOUND=1"
        goto :COPY_FILES
    )
)

if "%SUPPORT_FOUND%"=="0" (
    echo.
    echo Warning: AutoCAD Support folder not found automatically
    echo.
    echo Please copy the following files manually to your AutoCAD Support folder:
    echo - FreeScale_EN.lsp
    echo - FreeScale_EN.dcl
    echo.
    echo AutoCAD Support folder is usually located at:
    echo %APPDATA%\Autodesk\AutoCAD [VERSION]\Support
    echo.
    pause
    goto :END
)

:COPY_FILES
echo.
echo Copying files...

if exist "FreeScale_EN.lsp" (
    copy "FreeScale_EN.lsp" "%SUPPORT_PATH%\" >nul
    if %ERRORLEVEL%==0 (
        echo ✓ FreeScale_EN.lsp copied successfully
    ) else (
        echo ✗ Failed to copy FreeScale_EN.lsp
    )
) else (
    echo ✗ FreeScale_EN.lsp file not found
)

if exist "FreeScale_EN.dcl" (
    copy "FreeScale_EN.dcl" "%SUPPORT_PATH%\" >nul
    if %ERRORLEVEL%==0 (
        echo ✓ FreeScale_EN.dcl copied successfully
    ) else (
        echo ✗ Failed to copy FreeScale_EN.dcl
    )
) else (
    echo ✗ FreeScale_EN.dcl file not found
    echo ⚠️  Warning: GUI will not work without this file
)

echo.
echo ===============================================
echo Activation Instructions in AutoCAD:
echo ===============================================
echo.
echo 1. Open AutoCAD
echo.
echo 2. Type command: APPLOAD
echo.
echo 3. Select FreeScale_EN.lsp from the list
echo.
echo 4. Click Load then Close
echo.
echo 5. Type one of the following commands to use the tool:
echo    - FREESCALE (Main tool with GUI)
echo    - QUICKSCALE (Quick scaling)
echo    - MEASURE (Measure objects)
echo    - CONVERT (Unit converter)
echo.
echo ===============================================
echo For automatic loading when AutoCAD starts:
echo ===============================================
echo.
echo 1. Type command: APPLOAD
echo.
echo 2. Select FreeScale_EN.lsp
echo.
echo 3. Click Add to Startup Suite
echo.
echo 4. Click Close
echo.
echo Now the tool will load automatically when AutoCAD starts
echo.

:END
echo ===============================================
echo Installation Complete!
echo ===============================================
echo.
echo For help, refer to the following files:
echo - Installation_Guide_EN.txt (General guide)
echo - README.md (English documentation)
echo.
echo To test the tool:
echo 1. Open AutoCAD
echo 2. Type FREESCALE
echo 3. Select some objects
echo 4. Use the GUI to scale them
echo.
pause
